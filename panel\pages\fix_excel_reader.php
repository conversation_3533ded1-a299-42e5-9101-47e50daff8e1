<?php
// Script untuk memperbaiki Excel reader dan menganalisis file
echo "<h2>Excel Reader Fix & Analysis</h2>";

// Test dengan file template
$template_file = "../../file-excel/bee_kelas_temp.xls";

if (file_exists($template_file)) {
    echo "<h3>Analyzing Template File: $template_file</h3>";
    
    // Baca file sebagai binary
    $file_content = file_get_contents($template_file);
    echo "<p>File size: " . strlen($file_content) . " bytes</p>";
    
    // Cek header file
    $header = substr($file_content, 0, 8);
    $hex_header = bin2hex($header);
    echo "<p>File header (hex): $hex_header</p>";
    
    // Cek apakah ini file OLE/Excel yang valid
    if (substr($file_content, 0, 8) == "\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1") {
        echo "<p style='color: green;'>✓ Valid OLE/Excel file format detected</p>";
    } else {
        echo "<p style='color: red;'>✗ Invalid file format - not a proper Excel file</p>";
    }
    
    // Test dengan Excel reader original
    include "excel_reader2.php";
    
    try {
        echo "<h4>Testing Excel Reader...</h4>";
        $data = new Spreadsheet_Excel_Reader($template_file);
        
        // Debug internal state
        echo "<p>Sheets count: " . (isset($data->sheets) ? count($data->sheets) : 'NOT SET') . "</p>";
        
        if (isset($data->sheets)) {
            foreach ($data->sheets as $sheet_index => $sheet_data) {
                echo "<h5>Sheet $sheet_index Analysis:</h5>";
                echo "<ul>";
                
                // Cek semua properties
                $properties = ['numRows', 'numCols', 'maxrow', 'maxcol', 'cells'];
                foreach ($properties as $prop) {
                    if (isset($sheet_data[$prop])) {
                        if ($prop == 'cells' && is_array($sheet_data[$prop])) {
                            echo "<li>$prop: array with " . count($sheet_data[$prop]) . " elements</li>";
                            
                            // Show actual cell data
                            $cell_count = 0;
                            foreach ($sheet_data[$prop] as $row => $cols) {
                                if ($cell_count > 10) break;
                                if (is_array($cols)) {
                                    foreach ($cols as $col => $value) {
                                        if (trim($value) != '') {
                                            echo "<li>Cell [$row][$col]: '" . htmlspecialchars($value) . "'</li>";
                                            $cell_count++;
                                        }
                                        if ($cell_count > 10) break;
                                    }
                                }
                            }
                        } else {
                            echo "<li>$prop: " . $sheet_data[$prop] . "</li>";
                        }
                    } else {
                        echo "<li>$prop: NOT SET</li>";
                    }
                }
                echo "</ul>";
            }
        }
        
        // Test functions
        $baris = $data->rowcount(0);
        $kolom = $data->colcount(0);
        echo "<p>rowcount(0): $baris</p>";
        echo "<p>colcount(0): $kolom</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
}

// Test dengan file yang diupload
if (isset($_FILES['userfile']) && $_FILES['userfile']['error'] == UPLOAD_ERR_OK) {
    echo "<h3>Analyzing Uploaded File: " . $_FILES['userfile']['name'] . "</h3>";
    
    $uploaded_file = $_FILES['userfile']['tmp_name'];
    $file_content = file_get_contents($uploaded_file);
    echo "<p>File size: " . strlen($file_content) . " bytes</p>";
    
    // Cek header
    $header = substr($file_content, 0, 8);
    $hex_header = bin2hex($header);
    echo "<p>File header (hex): $hex_header</p>";
    
    if (substr($file_content, 0, 8) == "\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1") {
        echo "<p style='color: green;'>✓ Valid OLE/Excel file format</p>";
        
        // Test parsing
        try {
            $data = new Spreadsheet_Excel_Reader($uploaded_file);
            
            echo "<h4>Parsing Results:</h4>";
            if (isset($data->sheets)) {
                foreach ($data->sheets as $sheet_index => $sheet_data) {
                    echo "<h5>Sheet $sheet_index:</h5>";
                    
                    // Show all data found
                    if (isset($sheet_data['cells']) && is_array($sheet_data['cells'])) {
                        echo "<table border='1' style='border-collapse: collapse;'>";
                        echo "<tr><th>Row</th><th>Col</th><th>Value</th></tr>";
                        
                        $found_data = false;
                        foreach ($sheet_data['cells'] as $row => $cols) {
                            if (is_array($cols)) {
                                foreach ($cols as $col => $value) {
                                    if (trim($value) != '') {
                                        echo "<tr><td>$row</td><td>$col</td><td>" . htmlspecialchars($value) . "</td></tr>";
                                        $found_data = true;
                                    }
                                }
                            }
                        }
                        
                        if (!$found_data) {
                            echo "<tr><td colspan='3'>No data found in cells array</td></tr>";
                        }
                        
                        echo "</table>";
                    } else {
                        echo "<p>No cells data found</p>";
                    }
                }
            } else {
                echo "<p>No sheets found</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>Parsing error: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Invalid file format</p>";
        echo "<p>This might be an .xlsx file or corrupted .xls file</p>";
    }
}

// Form untuk upload test
if (!isset($_FILES['userfile'])) {
    echo "<h3>Upload Excel File for Analysis:</h3>";
    echo '<form method="post" enctype="multipart/form-data">';
    echo '<input name="userfile" type="file" accept=".xls" required>';
    echo '<input type="submit" value="Analyze File">';
    echo '</form>';
}

// Solusi alternatif
echo "<h3>Alternative Solutions:</h3>";
echo "<div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff;'>";
echo "<h4>Jika Excel reader tidak bisa diperbaiki:</h4>";
echo "<ol>";
echo "<li><strong>Gunakan format CSV:</strong> Buat file CSV dan upload sebagai .csv</li>";
echo "<li><strong>Gunakan Excel yang lebih lama:</strong> Save as Excel 97-2003 (.xls)</li>";
echo "<li><strong>Gunakan LibreOffice:</strong> Buka file di LibreOffice dan save as Excel 97-2003</li>";
echo "<li><strong>Buat template baru:</strong> Buat file Excel baru dengan data sample</li>";
echo "</ol>";
echo "</div>";

// Buat CSV converter
echo "<h3>CSV to Excel Converter:</h3>";
if (isset($_POST['create_csv_template'])) {
    $csv_content = "XKodeKelas,XKodeLevel,XNamaKelas,XKodeJurusan,XKodeSekolah\n";
    $csv_content .= "10A,10,Kelas 10 A,UMUM,WU25512\n";
    $csv_content .= "10B,10,Kelas 10 B,UMUM,WU25512\n";
    $csv_content .= "11IPA1,11,Kelas 11 IPA 1,IPA,WU25512\n";
    
    $filename = "template_kelas_" . date('Y-m-d_H-i-s') . ".csv";
    $filepath = "../../file-excel/" . $filename;
    
    file_put_contents($filepath, $csv_content);
    
    echo "<p style='color: green;'>✓ CSV template created: <a href='../../file-excel/$filename' target='_blank'>$filename</a></p>";
    echo "<p>Buka file ini di Excel dan save as .xls format</p>";
}

echo '<form method="post">';
echo '<button type="submit" name="create_csv_template">Create CSV Template</button>';
echo '</form>';
?>

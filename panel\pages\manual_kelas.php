<?php
if(!isset($_COOKIE['beeuser'])){
    header("Location: login.php");
}
?>

<div class="row">
    <div class="col-lg-10" style="margin-top:10px;">
        <div class="panel panel-default">
            <div class="panel-heading">
                Manual Class Entry (Workaround for Excel Issues)
            </div>
            <div class="panel-body">

<?php
if (isset($_POST['add_classes'])) {
    include "../../config/server.php";
    
    echo "<h4>Processing Class Data...</h4>";
    
    // Clear existing data if requested
    if (isset($_POST['clear_existing'])) {
        $query0 = "TRUNCATE TABLE cbt_kelas";
        $hasil0 = mysql_query($query0);
        if ($hasil0) {
            echo "<p style='color: green;'>✓ Cleared existing class data</p>";
        } else {
            echo "<p style='color: red;'>✗ Error clearing data: " . mysql_error() . "</p>";
        }
    }
    
    $sukses = 0;
    $gagal = 0;
    
    // Process each class entry
    for ($i = 1; $i <= 10; $i++) {
        $kode_kelas = trim($_POST["kode_kelas_$i"] ?? '');
        $kode_level = trim($_POST["kode_level_$i"] ?? '');
        $nama_kelas = trim($_POST["nama_kelas_$i"] ?? '');
        $kode_jurusan = trim($_POST["kode_jurusan_$i"] ?? '');
        $kode_sekolah = trim($_POST["kode_sekolah_$i"] ?? '');
        
        if ($kode_kelas != '' && $nama_kelas != '') {
            $query = "INSERT INTO cbt_kelas (XKodeKelas, XKodeLevel, XNamaKelas, XKodeJurusan, XStatusKelas, XKodeSekolah) VALUES ('$kode_kelas', '$kode_level', '$nama_kelas', '$kode_jurusan', '1', '$kode_sekolah')";
            
            $hasil = mysql_query($query);
            if ($hasil) {
                $sukses++;
                echo "<p style='color: green;'>✓ Added: $nama_kelas ($kode_kelas)</p>";
            } else {
                $gagal++;
                echo "<p style='color: red;'>✗ Failed: $nama_kelas - " . mysql_error() . "</p>";
            }
        }
    }
    
    echo "<div style='background-color: #d4edda; padding: 10px; margin: 10px 0; border: 1px solid #c3e6cb;'>";
    echo "<strong>Summary:</strong><br>";
    echo "Successfully added: $sukses classes<br>";
    echo "Failed: $gagal classes";
    echo "</div>";
    
    if ($sukses > 0) {
        echo "<p style='color: green; font-weight: bold;'>✓ Classes have been added! You can now upload student data.</p>";
    }
}

// Show current classes
include "../../config/server.php";
$existing_query = mysql_query("SELECT * FROM cbt_kelas ORDER BY XKodeLevel, XKodeKelas");
$existing_count = mysql_num_rows($existing_query);

if ($existing_count > 0) {
    echo "<h4>Current Classes in Database ($existing_count):</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Kode Kelas</th><th>Level</th><th>Nama Kelas</th><th>Jurusan</th><th>Kode Sekolah</th></tr>";
    while ($class = mysql_fetch_array($existing_query)) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($class['XKodeKelas']) . "</td>";
        echo "<td>" . htmlspecialchars($class['XKodeLevel']) . "</td>";
        echo "<td>" . htmlspecialchars($class['XNamaKelas']) . "</td>";
        echo "<td>" . htmlspecialchars($class['XKodeJurusan']) . "</td>";
        echo "<td>" . htmlspecialchars($class['XKodeSekolah']) . "</td>";
        echo "</tr>";
    }
    echo "</table><br>";
}
?>

<form method="post">
    <h4>Add New Classes:</h4>
    
    <label>
        <input type="checkbox" name="clear_existing" value="1"> 
        Clear existing classes first (check this if you want to replace all current classes)
    </label><br><br>
    
    <table border="1" style="border-collapse: collapse; width: 100%;">
        <tr>
            <th>Kode Kelas</th>
            <th>Kode Level</th>
            <th>Nama Kelas</th>
            <th>Kode Jurusan</th>
            <th>Kode Sekolah</th>
        </tr>
        <?php for ($i = 1; $i <= 10; $i++): ?>
        <tr>
            <td><input type="text" name="kode_kelas_<?php echo $i; ?>" placeholder="e.g., 10A" style="width: 100%;"></td>
            <td><input type="text" name="kode_level_<?php echo $i; ?>" placeholder="e.g., 10" style="width: 100%;"></td>
            <td><input type="text" name="nama_kelas_<?php echo $i; ?>" placeholder="e.g., Kelas 10 A" style="width: 100%;"></td>
            <td><input type="text" name="kode_jurusan_<?php echo $i; ?>" placeholder="e.g., UMUM" style="width: 100%;"></td>
            <td><input type="text" name="kode_sekolah_<?php echo $i; ?>" placeholder="e.g., WU25512" style="width: 100%;"></td>
        </tr>
        <?php endfor; ?>
    </table>
    
    <br>
    <button type="submit" name="add_classes" class="btn btn-primary">Add Classes</button>
</form>

<div style="background-color: #f8f9fa; padding: 15px; margin: 15px 0; border-left: 4px solid #007bff;">
    <h4>Sample Data:</h4>
    <p>Here are some example values you can use:</p>
    <ul>
        <li><strong>Kode Kelas:</strong> 10A, 10B, 11IPA1, 11IPS1, 12IPA1, etc.</li>
        <li><strong>Kode Level:</strong> 10, 11, 12</li>
        <li><strong>Nama Kelas:</strong> Kelas 10 A, Kelas 11 IPA 1, etc.</li>
        <li><strong>Kode Jurusan:</strong> UMUM, IPA, IPS</li>
        <li><strong>Kode Sekolah:</strong> WU25512 (or your school code)</li>
    </ul>
</div>

            </div>
        </div>
    </div>
</div>

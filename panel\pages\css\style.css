/* Modern Professional Color Palette for CBT Admin Panel */
:root {
    /* Primary Colors */
    --primary-blue: #2563eb;
    --primary-blue-dark: #1d4ed8;
    --primary-blue-light: #3b82f6;

    /* Secondary Colors */
    --secondary-slate: #475569;
    --secondary-slate-dark: #334155;
    --secondary-slate-light: #64748b;

    /* Accent Colors */
    --accent-emerald: #10b981;
    --accent-amber: #f59e0b;
    --accent-rose: #f43f5e;
    --accent-violet: #8b5cf6;

    /* Neutral Colors */
    --neutral-50: #f8fafc;
    --neutral-100: #f1f5f9;
    --neutral-200: #e2e8f0;
    --neutral-300: #cbd5e1;
    --neutral-400: #94a3b8;
    --neutral-500: #64748b;
    --neutral-600: #475569;
    --neutral-700: #334155;
    --neutral-800: #1e293b;
    --neutral-900: #0f172a;

    /* Semantic Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;

    /* Text Colors */
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --text-inverse: #ffffff;

    /* Border Colors */
    --border-light: #e2e8f0;
    --border-medium: #cbd5e1;
    --border-dark: #94a3b8;
}

body {
    background: var(--bg-secondary);
    font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    padding: 20px;
    color: var(--text-primary);
    line-height: 1.6;
}

.box {
    background: var(--bg-primary);
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid var(--border-light);
    transition: box-shadow 0.2s ease-in-out;
}

.box:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.box > h3 {
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    padding: 24px 24px 0 24px;
    margin: 0;
}

.box > p {
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    padding: 12px 24px 24px 24px;
    margin: 0;
}

/* Enhanced Table Styling */
.bdt {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--border-light);
    background: var(--bg-primary);
}

.bdt thead {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
}

.bdt > thead > tr > th {
    font-size: 13px;
    font-weight: 600;
    color: var(--text-inverse);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 16px 20px;
    border: none;
}

.bdt > tbody > tr > td {
    font-size: 13px;
    color: var(--text-primary);
    padding: 16px 20px;
    border-color: var(--border-light);
    vertical-align: middle;
}

.bdt tr:last-child td, .bdt tr:last-child th {
    border-bottom: 1px solid var(--border-light);
}

.bdt > tbody > tr:hover {
    background-color: var(--neutral-50);
    transition: background-color 0.2s ease-in-out;
}

.bdt > tbody > tr > td, .bdt > tbody > tr > th, .bdt > tfoot > tr > td, .bdt > tfoot > tr > th, .bdt > thead > tr > td, .bdt > thead > tr > th {
    border-color: var(--border-light);
    padding: 16px 20px;
}

/* Enhanced Form Controls */
.search-form .form-control {
    border: 2px solid var(--border-light);
    border-radius: 8px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    font-weight: 400;
    height: 48px;
    margin-left: 20px;
    padding: 12px 16px;
    transition: all 0.2s ease-in-out;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.search-form .form-control:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

#page-rows-form {
    padding-left: 20px;
}

#page-rows-form label {
    line-height: 48px;
    padding-right: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

#page-rows-form select {
    background: var(--bg-primary);
    border: 2px solid var(--border-light);
    border-radius: 8px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    font-weight: 400;
    height: 48px;
    margin: 0;
    outline: none;
    padding: 12px 16px;
    color: var(--text-primary);
    transition: all 0.2s ease-in-out;
}

#page-rows-form select:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Enhanced Pagination and Table Footer */
#table-footer {
    margin-bottom: 24px;
    padding: 16px 0;
}

.pagination {
    padding-right: 20px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.pagination > .disabled > span, .pagination > .disabled > span:hover, .pagination > .disabled > span:focus, .pagination > .disabled > a, .pagination > .disabled > a:hover, .pagination > .disabled > a:focus {
    border-color: var(--border-light);
    background: var(--neutral-100);
    color: var(--text-muted);
    cursor: not-allowed;
}

.pagination > li {
    display: inline-block;
    margin: 0 2px;
}

.pagination > li > a, .pagination > li > span {
    border: 1px solid var(--border-light);
    border-radius: 6px;
    transition: all 0.2s ease-in-out;
}

.pagination > li > a, .pagination > li > span {
    background: var(--bg-primary);
    height: 40px;
    line-height: 38px;
    padding: 0;
    text-align: center;
    width: 40px;
    color: var(--text-primary);
    font-weight: 500;
    text-decoration: none;
}

.pagination > li > a:hover, .pagination > li > span:hover {
    background: var(--primary-blue);
    color: var(--text-inverse);
    border-color: var(--primary-blue);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
}

.pagination > li:last-child > a, .pagination > li:last-child > span {
    border-radius: 6px;
    color: var(--text-primary);
}

.pagination > li:first-child > a, .pagination > li:first-child > span {
    border-radius: 6px;
}

.pagination > li.active > a, .pagination > li.active > span {
    font-weight: 600;
    background: var(--primary-blue);
    color: var(--text-inverse);
    border-color: var(--primary-blue);
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.3);
}

.table-info {
    font-size: 13px;
    line-height: 40px;
    margin-left: 20px;
    color: var(--text-secondary);
    font-weight: 500;
}

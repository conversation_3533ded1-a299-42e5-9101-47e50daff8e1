<?php
// Test script to examine the template file structure
include "excel_reader2.php";

$template_file = "../../file-excel/bee_siswa_temp.xls";

echo "<h3>Template File Analysis</h3>";

if (!file_exists($template_file)) {
    echo "<p style='color: red;'>Template file not found: $template_file</p>";
    exit;
}

try {
    echo "<p>Reading template file: $template_file</p>";
    $data = new Spreadsheet_Excel_Reader($template_file);
    
    echo "<p>Template loaded successfully!</p>";
    
    // Get row and column count
    $baris = $data->rowcount(0);
    $kolom = $data->colcount(0);
    
    echo "<p>Template has $baris rows and $kolom columns</p>";
    
    // Show the template structure
    echo "<h4>Template Structure:</h4>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    
    for ($i = 1; $i <= min(10, $baris); $i++) {
        echo "<tr>";
        echo "<td><strong>Row $i</strong></td>";
        for ($j = 1; $j <= min(15, $kolom); $j++) {
            $value = $data->val($i, $j, 0);
            echo "<td>" . htmlspecialchars($value) . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    
    // Show column headers (assuming they're in row 1 or 2)
    echo "<h4>Column Headers Analysis:</h4>";
    echo "<p>Row 1:</p><ul>";
    for ($j = 1; $j <= min(15, $kolom); $j++) {
        $value = $data->val(1, $j, 0);
        echo "<li>Column $j: '" . htmlspecialchars($value) . "'</li>";
    }
    echo "</ul>";
    
    echo "<p>Row 2:</p><ul>";
    for ($j = 1; $j <= min(15, $kolom); $j++) {
        $value = $data->val(2, $j, 0);
        echo "<li>Column $j: '" . htmlspecialchars($value) . "'</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

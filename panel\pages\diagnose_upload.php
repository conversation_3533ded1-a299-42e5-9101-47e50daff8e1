<?php
// Comprehensive diagnostic script for Excel upload issues
echo "<h2>Excel Upload Diagnostic Tool</h2>";

// Step 1: Check database connection
echo "<h3>Step 1: Database Connection Test</h3>";
try {
    include "../../config/server.php";
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Test basic query
    $test_query = mysql_query("SELECT COUNT(*) as count FROM cbt_siswa");
    if ($test_query) {
        $result = mysql_fetch_array($test_query);
        echo "<p>✓ Current students in database: " . $result['count'] . "</p>";
    } else {
        echo "<p style='color: red;'>✗ Error querying cbt_siswa table: " . mysql_error() . "</p>";
    }
    
    // Check if cbt_kelas has data (required for validation)
    $kelas_query = mysql_query("SELECT COUNT(*) as count FROM cbt_kelas");
    if ($kelas_query) {
        $kelas_result = mysql_fetch_array($kelas_query);
        echo "<p>✓ Classes in database: " . $kelas_result['count'] . "</p>";
        if ($kelas_result['count'] == 0) {
            echo "<p style='color: orange;'>⚠ Warning: No classes found in cbt_kelas table. You need to add classes first!</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
}

// Step 2: Check Excel reader
echo "<h3>Step 2: Excel Reader Test</h3>";
try {
    include "excel_reader2.php";
    echo "<p style='color: green;'>✓ Excel reader loaded successfully</p>";
    
    // Test with template file
    $template_file = "../../file-excel/bee_siswa_temp.xls";
    if (file_exists($template_file)) {
        echo "<p>✓ Template file exists: $template_file</p>";
        
        $data = new Spreadsheet_Excel_Reader($template_file);
        $baris = $data->rowcount(0);
        $kolom = $data->colcount(0);
        
        echo "<p>✓ Template readable - Rows: $baris, Columns: $kolom</p>";
        
        // Show template structure
        echo "<h4>Template Structure (first 5 rows):</h4>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        for ($i = 1; $i <= min(5, $baris); $i++) {
            echo "<tr>";
            echo "<td><strong>Row $i</strong></td>";
            for ($j = 1; $j <= min(15, $kolom); $j++) {
                $value = $data->val($i, $j, 0);
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p style='color: red;'>✗ Template file not found: $template_file</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Excel reader error: " . $e->getMessage() . "</p>";
}

// Step 3: File upload test
echo "<h3>Step 3: File Upload Test</h3>";

if (isset($_FILES['userfile']) && $_FILES['userfile']['error'] == UPLOAD_ERR_OK) {
    echo "<p>✓ File uploaded: " . $_FILES['userfile']['name'] . "</p>";
    echo "<p>✓ File size: " . $_FILES['userfile']['size'] . " bytes</p>";
    echo "<p>✓ File type: " . $_FILES['userfile']['type'] . "</p>";
    
    try {
        $data = new Spreadsheet_Excel_Reader($_FILES['userfile']['tmp_name']);
        $baris = $data->rowcount(0);
        $kolom = $data->colcount(0);
        
        echo "<p>✓ File readable - Rows: $baris, Columns: $kolom</p>";
        
        if ($baris > 0) {
            echo "<h4>File Content Analysis:</h4>";
            
            // Check data starting from row 3 (as per upload script)
            $valid_rows = 0;
            $empty_rows = 0;
            
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Row</th><th>Student ID</th><th>Name</th><th>NIK</th><th>Class</th><th>Status</th></tr>";
            
            for ($i = 3; $i <= min(10, $baris); $i++) {
                $xnomer = $data->val($i, 1);
                $xnama = $data->val($i, 2);
                $xnik = $data->val($i, 3);
                $xkelas = $data->val($i, 7);
                
                $xnomer_clean = str_replace(" ", "", $xnomer);
                
                echo "<tr>";
                echo "<td>$i</td>";
                echo "<td>" . htmlspecialchars($xnomer) . "</td>";
                echo "<td>" . htmlspecialchars($xnama) . "</td>";
                echo "<td>" . htmlspecialchars($xnik) . "</td>";
                echo "<td>" . htmlspecialchars($xkelas) . "</td>";
                
                if ($xnomer_clean != "") {
                    echo "<td style='color: green;'>Valid</td>";
                    $valid_rows++;
                } else {
                    echo "<td style='color: red;'>Empty ID</td>";
                    $empty_rows++;
                }
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<p><strong>Summary:</strong></p>";
            echo "<ul>";
            echo "<li>Valid rows found: $valid_rows</li>";
            echo "<li>Empty rows found: $empty_rows</li>";
            echo "<li>Total data rows (3 to $baris): " . ($baris - 2) . "</li>";
            echo "</ul>";
            
            if ($valid_rows == 0) {
                echo "<p style='color: red;'>⚠ <strong>ISSUE FOUND:</strong> No valid student IDs found in column 1!</p>";
                echo "<p>Make sure your Excel file has student numbers in the first column starting from row 3.</p>";
            }
            
        } else {
            echo "<p style='color: red;'>✗ File has no data rows</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error reading uploaded file: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p>No file uploaded. Upload an Excel file to test:</p>";
    echo '<form method="post" enctype="multipart/form-data">';
    echo '<input name="userfile" type="file" accept=".xls" required>';
    echo '<input type="submit" value="Diagnose File" style="margin-left: 10px;">';
    echo '</form>';
    
    if (isset($_FILES['userfile'])) {
        echo "<p style='color: red;'>Upload error code: " . $_FILES['userfile']['error'] . "</p>";
    }
}

// Step 4: Recommendations
echo "<h3>Step 4: Recommendations</h3>";
echo "<div style='background-color: #f0f8ff; padding: 15px; border-left: 4px solid #0066cc;'>";
echo "<h4>To fix upload issues:</h4>";
echo "<ol>";
echo "<li><strong>Use the correct template:</strong> Download from the system (bee_siswa_temp.xls)</li>";
echo "<li><strong>Check file format:</strong> Must be .xls (not .xlsx)</li>";
echo "<li><strong>Ensure data starts from row 3:</strong> Rows 1-2 are headers</li>";
echo "<li><strong>Student ID required:</strong> Column 1 must have student numbers</li>";
echo "<li><strong>Add classes first:</strong> Make sure cbt_kelas table has class data</li>";
echo "<li><strong>Check validation:</strong> Class codes in Excel must match database</li>";
echo "</ol>";
echo "</div>";
?>

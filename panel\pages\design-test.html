<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Design System Test - CBT Admin Panel</title>

    <!-- Bootstrap Core CSS -->
    <link href="../vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Modern Admin Theme CSS -->
    <link href="css/modern-admin.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">

    <style>
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--border-light);
            border-radius: 8px;
            background: var(--bg-primary);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            font-weight: 500;
        }
        .test-pass {
            background: rgba(16, 185, 129, 0.1);
            color: #065f46;
            border-left: 4px solid var(--accent-emerald);
        }
        .test-fail {
            background: rgba(244, 63, 94, 0.1);
            color: #991b1b;
            border-left: 4px solid var(--accent-rose);
        }
        .color-test {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            display: inline-block;
            margin: 5px;
            border: 1px solid var(--border-light);
        }
    </style>
</head>

<body class="modern-admin-body">
    <div class="container-fluid">
        <div class="page-header modern-page-header">
            <h1><i class="fa fa-check-circle"></i> Design System Test</h1>
            <p class="text-secondary">Automated tests to validate the modern design implementation</p>
        </div>

        <!-- CSS Variables Test -->
        <div class="test-section">
            <h3>CSS Variables Test</h3>
            <p>Testing if CSS custom properties are properly loaded and accessible.</p>
            
            <div class="row">
                <div class="col-md-6">
                    <h4>Primary Colors</h4>
                    <div class="color-test" style="background: var(--primary-blue);" title="Primary Blue"></div>
                    <div class="color-test" style="background: var(--primary-blue-dark);" title="Primary Blue Dark"></div>
                    <div class="color-test" style="background: var(--primary-blue-light);" title="Primary Blue Light"></div>
                </div>
                <div class="col-md-6">
                    <h4>Accent Colors</h4>
                    <div class="color-test" style="background: var(--accent-emerald);" title="Success Green"></div>
                    <div class="color-test" style="background: var(--accent-amber);" title="Warning Amber"></div>
                    <div class="color-test" style="background: var(--accent-rose);" title="Error Rose"></div>
                </div>
            </div>
            
            <div id="css-variables-result" class="test-result"></div>
        </div>

        <!-- Button Test -->
        <div class="test-section">
            <h3>Button Component Test</h3>
            <p>Testing button styling and hover effects.</p>
            
            <div class="btn-group" role="group">
                <button class="btn btn-primary" id="test-btn-primary">Primary</button>
                <button class="btn btn-success" id="test-btn-success">Success</button>
                <button class="btn btn-warning" id="test-btn-warning">Warning</button>
                <button class="btn btn-danger" id="test-btn-danger">Danger</button>
                <button class="btn btn-secondary" id="test-btn-secondary">Secondary</button>
            </div>
            
            <div id="button-test-result" class="test-result"></div>
        </div>

        <!-- Form Test -->
        <div class="test-section">
            <h3>Form Component Test</h3>
            <p>Testing form control styling and focus states.</p>
            
            <form>
                <div class="form-group">
                    <label class="modern-form-label">Test Input</label>
                    <input type="text" class="form-control modern-form-control" id="test-input" placeholder="Test input field">
                </div>
                <div class="form-group">
                    <label class="modern-form-label">Test Select</label>
                    <select class="form-control modern-form-control" id="test-select">
                        <option>Option 1</option>
                        <option>Option 2</option>
                    </select>
                </div>
            </form>
            
            <div id="form-test-result" class="test-result"></div>
        </div>

        <!-- Table Test -->
        <div class="test-section">
            <h3>Table Component Test</h3>
            <p>Testing table styling and hover effects.</p>
            
            <table class="table table-striped table-bordered table-hover modern-table bdt" id="test-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>Test Item 1</td>
                        <td><span class="status-active">Active</span></td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>Test Item 2</td>
                        <td><span class="status-pending">Pending</span></td>
                    </tr>
                </tbody>
            </table>
            
            <div id="table-test-result" class="test-result"></div>
        </div>

        <!-- Panel Test -->
        <div class="test-section">
            <h3>Panel Component Test</h3>
            <p>Testing panel styling and modern classes.</p>
            
            <div class="panel panel-default modern-panel" id="test-panel">
                <div class="panel-heading modern-panel-heading">
                    <i class="fa fa-test"></i> Test Panel
                </div>
                <div class="panel-body">
                    This is a test panel with modern styling applied.
                </div>
            </div>
            
            <div id="panel-test-result" class="test-result"></div>
        </div>

        <!-- Responsive Test -->
        <div class="test-section">
            <h3>Responsive Design Test</h3>
            <p>Testing responsive behavior at different screen sizes.</p>
            
            <div class="row">
                <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
                    <div class="modern-card">
                        <div class="modern-card-header">Responsive Card 1</div>
                        <div class="modern-card-body">Content adapts to screen size</div>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
                    <div class="modern-card">
                        <div class="modern-card-header">Responsive Card 2</div>
                        <div class="modern-card-body">Content adapts to screen size</div>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
                    <div class="modern-card">
                        <div class="modern-card-header">Responsive Card 3</div>
                        <div class="modern-card-body">Content adapts to screen size</div>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
                    <div class="modern-card">
                        <div class="modern-card-header">Responsive Card 4</div>
                        <div class="modern-card-body">Content adapts to screen size</div>
                    </div>
                </div>
            </div>
            
            <div id="responsive-test-result" class="test-result"></div>
        </div>

        <!-- Test Results Summary -->
        <div class="test-section">
            <h3>Test Results Summary</h3>
            <div id="overall-test-result" class="test-result"></div>
        </div>
    </div>

    <script>
        // Simple test runner
        document.addEventListener('DOMContentLoaded', function() {
            let passedTests = 0;
            let totalTests = 0;

            function runTest(testName, testFunction, resultElementId) {
                totalTests++;
                try {
                    const result = testFunction();
                    const resultElement = document.getElementById(resultElementId);
                    if (result) {
                        resultElement.className = 'test-result test-pass';
                        resultElement.innerHTML = `✓ ${testName}: PASSED`;
                        passedTests++;
                    } else {
                        resultElement.className = 'test-result test-fail';
                        resultElement.innerHTML = `✗ ${testName}: FAILED`;
                    }
                } catch (error) {
                    const resultElement = document.getElementById(resultElementId);
                    resultElement.className = 'test-result test-fail';
                    resultElement.innerHTML = `✗ ${testName}: ERROR - ${error.message}`;
                }
            }

            // Test CSS Variables
            runTest('CSS Variables', function() {
                const testElement = document.createElement('div');
                testElement.style.color = 'var(--primary-blue)';
                document.body.appendChild(testElement);
                const computedStyle = window.getComputedStyle(testElement);
                const color = computedStyle.color;
                document.body.removeChild(testElement);
                return color !== '' && color !== 'var(--primary-blue)';
            }, 'css-variables-result');

            // Test Button Styling
            runTest('Button Styling', function() {
                const button = document.getElementById('test-btn-primary');
                const computedStyle = window.getComputedStyle(button);
                return computedStyle.borderRadius !== '0px' && computedStyle.borderRadius !== '';
            }, 'button-test-result');

            // Test Form Controls
            runTest('Form Controls', function() {
                const input = document.getElementById('test-input');
                const computedStyle = window.getComputedStyle(input);
                return computedStyle.borderRadius !== '0px' && computedStyle.borderRadius !== '';
            }, 'form-test-result');

            // Test Table Styling
            runTest('Table Styling', function() {
                const table = document.getElementById('test-table');
                const computedStyle = window.getComputedStyle(table);
                return computedStyle.borderRadius !== '0px' && computedStyle.borderRadius !== '';
            }, 'table-test-result');

            // Test Panel Styling
            runTest('Panel Styling', function() {
                const panel = document.getElementById('test-panel');
                const computedStyle = window.getComputedStyle(panel);
                return computedStyle.borderRadius !== '0px' && computedStyle.borderRadius !== '';
            }, 'panel-test-result');

            // Test Responsive Design
            runTest('Responsive Design', function() {
                return window.innerWidth > 0; // Basic check that viewport is working
            }, 'responsive-test-result');

            // Update overall results
            setTimeout(function() {
                const overallResult = document.getElementById('overall-test-result');
                const percentage = Math.round((passedTests / totalTests) * 100);
                
                if (passedTests === totalTests) {
                    overallResult.className = 'test-result test-pass';
                    overallResult.innerHTML = `🎉 All tests passed! (${passedTests}/${totalTests}) - ${percentage}%`;
                } else {
                    overallResult.className = 'test-result test-fail';
                    overallResult.innerHTML = `⚠️ ${passedTests}/${totalTests} tests passed - ${percentage}%`;
                }
            }, 100);
        });
    </script>
</body>
</html>

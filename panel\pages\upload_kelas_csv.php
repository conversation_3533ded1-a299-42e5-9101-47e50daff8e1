<?php
if(!isset($_COOKIE['beeuser'])){
    header("Location: login.php");
}

if(isset($_REQUEST['modul'])){
    if($_REQUEST['modul']=="upl_kelas"){
    $kata = "Data Kelas"; }
    elseif($_REQUEST['modul']=="upl_mapel"){
    $kata = "Data Mata Pelajaran"; }
    elseif($_REQUEST['modul']=="upl_siswa"){
    $kata = "Data Siswa"; }
}
?>

<div class="row">
    <div class="col-lg-10" style="margin-top:10px;">
        <div class="panel panel-green">
            <div class="panel-heading">
               Download Template CSV Kelas
            </div>
            <div class="panel-body">
                <div style="width: 20%; float:left">
                   <a href="../../file-excel/template_kelas.csv" target="_blank"><img src="images/csv.png" style=" width:90%; max-width:100px;padding-right:10px;" onerror="this.src='images/xls.png'"/></a>
                </div>
                <div style="width: 80%; float:right">
                   Silahkan Klik logo CSV disamping, untuk <b> download </b> template CSV database Kelas. 
                   <br><span style="color: #ff0000;">Format: XKodeKelas,XKodeLevel,XNamaKelas,XKodeJurusan,XKodeSekolah</span>
                   <p>Setelah selesai edit, Upload kembali untuk ditransfer ke database melalui tool dibawah ini. 
                </div>
                <div style="clear:both;"></div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-10" style="margin-top:10px;">
        <div class="panel panel-default">
            <div class="panel-heading">
                Upload CSV/Excel - Kelas (Mendukung .csv dan .xls)
            </div>
            <div class="panel-body">
                <form method="post" enctype="multipart/form-data" action="<?php echo "?modul=uploadkelas_csv"; ?>">
                File CSV/Excel Daftar Kelas : 
                <table border="0" width="78%" cellpadding="20px" cellspacing="20px">
                    <tr>
                        <td width="30%">
                            <input name="userfile" type="file" class="btn btn-default" style="width:250px" accept=".csv,.xls">
                        </td>
                        <td>
                            &nbsp;<input name="upload" type="submit" value="Import"  class="btn btn-info" style="margin-top:0px">
                        </td>
                    </tr>
                </table>
                </form>
                <div style="margin-top:10px;">Persentase Proses Upload <?php echo $kata; ?></div>
                
                <!-- Progress bar holder -->
                <div id="progress" style="width:75%; border:1px solid #ccc; padding:5px; margin-top:10px; height:33px"></div>
                <!-- Progress information -->
                <div id="information" style="width"></div>

<?php
if($_REQUEST['modul']=="uploadkelas_csv"){
    
    // Check if file was uploaded successfully
    if (!isset($_FILES['userfile']) || $_FILES['userfile']['error'] != UPLOAD_ERR_OK) {
        echo "<div style='color: red; font-weight: bold;'>Error: File upload failed!</div>";
        exit;
    }

    // Include database connection
    include "../../config/server.php";
    
    $file_path = $_FILES['userfile']['tmp_name'];
    $file_name = $_FILES['userfile']['name'];
    $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
    
    echo "<div>Processing file: $file_name (.$file_ext)</div>";
    
    $sukses = 0;
    $gagal = 0;
    
    // Clear existing data
    $query0 = "TRUNCATE TABLE cbt_kelas";
    $hasil0 = mysql_query($query0);
    
    if (!$hasil0) {
        echo "<div style='color: red; font-weight: bold;'>Error truncating table: " . mysql_error() . "</div>";
        exit;
    }
    
    echo "<br><table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Status</th><th>Detail</th></tr>";
    
    if ($file_ext == 'csv') {
        // Process CSV file
        echo "<div>Processing CSV file...</div>";
        
        $handle = fopen($file_path, 'r');
        if ($handle) {
            $row_num = 0;
            while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $row_num++;
                
                // Skip header row
                if ($row_num == 1) {
                    echo "<tr><td>Header</td><td>Skipped: " . implode(', ', $data) . "</td></tr>";
                    continue;
                }
                
                // Ensure we have 5 columns
                if (count($data) >= 5) {
                    $x1 = trim($data[0]); // XKodeKelas
                    $x2 = trim($data[1]); // XKodeLevel
                    $x3 = trim($data[2]); // XNamaKelas
                    $x4 = trim($data[3]); // XKodeJurusan
                    $x5 = trim($data[4]); // XKodeSekolah
                    
                    if ($x1 != "") {
                        $x1 = mysql_real_escape_string($x1);
                        $x2 = mysql_real_escape_string($x2);
                        $x3 = mysql_real_escape_string($x3);
                        $x4 = mysql_real_escape_string($x4);
                        $x5 = mysql_real_escape_string($x5);
                        
                        $query = "INSERT INTO cbt_kelas (XKodeKelas, XKodeLevel, XNamaKelas, XKodeJurusan, XStatusKelas, XKodeSekolah) VALUES ('$x1','$x2', '$x3', '$x4','1', '$x5')";
                        $hasil = mysql_query($query);
                        
                        if ($hasil) {
                            $sukses++;
                            echo "<tr><td>Berhasil</td><td>Insert data Kelas <b>$x3</b> ($x1)</td></tr>";
                        } else {
                            $gagal++;
                            echo "<tr><td style='color: red;'>Gagal</td><td>Insert data Kelas <b>$x3</b> - Error: " . mysql_error() . "</td></tr>";
                        }
                    } else {
                        $gagal++;
                        echo "<tr><td style='color: orange;'>Skip</td><td>Baris $row_num - Kode kelas kosong</td></tr>";
                    }
                } else {
                    $gagal++;
                    echo "<tr><td style='color: red;'>Error</td><td>Baris $row_num - Data tidak lengkap (butuh 5 kolom)</td></tr>";
                }
                
                // Progress update
                $percent = intval($row_num/100 * 100)."%";
                echo '<script language="javascript">
                document.getElementById("progress").innerHTML="<div style=\"width:'.$percent.';background-image:url(images/pbar-ani1.gif);\">&nbsp;</div>";
                document.getElementById("information").innerHTML="Proses Entri : '.$x3.' ... <b>'.$row_num.'</b> rows processed.";
                </script>';
                echo str_repeat(' ',1024*64);
                flush();
            }
            fclose($handle);
        } else {
            echo "<div style='color: red;'>Error: Cannot read CSV file</div>";
        }
        
    } else if ($file_ext == 'xls') {
        // Process Excel file (fallback to original method)
        echo "<div>Processing Excel file...</div>";
        
        include "excel_reader2.php";
        
        try {
            $data = new Spreadsheet_Excel_Reader($file_path);
            $baris = $data->rowcount(0);
            
            echo "<div>Excel file has $baris rows</div>";
            
            if ($baris > 0) {
                for ($i=2; $i<=$baris; $i++) {
                    $x1 = trim($data->val($i, 1));
                    $x2 = trim($data->val($i, 2));
                    $x3 = trim($data->val($i, 3));
                    $x4 = trim($data->val($i, 4));
                    $x5 = trim($data->val($i, 5));
                    
                    if ($x1 != "") {
                        $x1 = mysql_real_escape_string($x1);
                        $x2 = mysql_real_escape_string($x2);
                        $x3 = mysql_real_escape_string($x3);
                        $x4 = mysql_real_escape_string($x4);
                        $x5 = mysql_real_escape_string($x5);
                        
                        $query = "INSERT INTO cbt_kelas (XKodeKelas, XKodeLevel, XNamaKelas, XKodeJurusan, XStatusKelas, XKodeSekolah) VALUES ('$x1','$x2', '$x3', '$x4','1', '$x5')";
                        $hasil = mysql_query($query);
                        
                        if ($hasil) {
                            $sukses++;
                            echo "<tr><td>Berhasil</td><td>Insert data Kelas <b>$x3</b> ($x1)</td></tr>";
                        } else {
                            $gagal++;
                            echo "<tr><td style='color: red;'>Gagal</td><td>Insert data Kelas <b>$x3</b> - Error: " . mysql_error() . "</td></tr>";
                        }
                    }
                    
                    $percent = intval($i/$baris * 100)."%";
                    echo '<script language="javascript">
                    document.getElementById("progress").innerHTML="<div style=\"width:'.$percent.';background-image:url(images/pbar-ani1.gif);\">&nbsp;</div>";
                    document.getElementById("information").innerHTML="Proses Entri : '.$x3.' ... <b>'.$i.'</b> row(s) of <b>'. $baris.'</b> processed.";
                    </script>';
                    echo str_repeat(' ',1024*64);
                    flush();
                }
            } else {
                echo "<tr><td style='color: red;'>Error</td><td>Excel file tidak memiliki data atau tidak bisa dibaca</td></tr>";
            }
            
        } catch (Exception $e) {
            echo "<tr><td style='color: red;'>Error</td><td>Excel parsing error: " . $e->getMessage() . "</td></tr>";
        }
        
    } else {
        echo "<div style='color: red;'>Error: File format tidak didukung. Gunakan .csv atau .xls</div>";
    }
    
    echo "</table>";
    
    echo '<script language="javascript">document.getElementById("information").innerHTML="Proses update database Kelas : Completed"</script>';
}
?>

            </div>
        </div>
    </div>
</div>

<!-- Summary -->
<?php if(isset($_REQUEST['modul']) && $_REQUEST['modul']=="uploadkelas_csv"): ?>
<div style="width:75%; margin-top:10px">
    <div class="alert alert-success">
        <p>Jumlah data yang sukses diimport : <?php echo $sukses; ?><br>
    </div>
    
    <?php if($gagal>0): ?>
    <div class="alert alert-danger">
        <p>Jumlah data yang gagal diimport : <?php echo $gagal; ?><br>
    </div>
    <?php endif; ?>
</div>
<?php endif; ?>

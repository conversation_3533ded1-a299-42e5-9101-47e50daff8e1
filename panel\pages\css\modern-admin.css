/* Modern CBT Admin Panel Theme */
/* Professional Color Palette and Enhanced UI Components */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Enhanced <PERSON><PERSON> Styles */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 12px 24px;
    font-size: 14px;
    line-height: 1.4;
    transition: all 0.2s ease-in-out;
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    text-decoration: none;
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
    color: var(--text-inverse);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-blue-dark) 0%, #1e40af 100%);
    color: var(--text-inverse);
}

.btn-success {
    background: linear-gradient(135deg, var(--accent-emerald) 0%, #059669 100%);
    color: var(--text-inverse);
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    color: var(--text-inverse);
}

.btn-warning {
    background: linear-gradient(135deg, var(--accent-amber) 0%, #d97706 100%);
    color: var(--text-inverse);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    color: var(--text-inverse);
}

.btn-danger {
    background: linear-gradient(135deg, var(--accent-rose) 0%, #dc2626 100%);
    color: var(--text-inverse);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: var(--text-inverse);
}

.btn-default, .btn-secondary {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 2px solid var(--border-medium);
}

.btn-default:hover, .btn-secondary:hover {
    background: var(--neutral-50);
    color: var(--text-primary);
    border-color: var(--border-dark);
}

.btn-small {
    padding: 8px 16px;
    font-size: 13px;
}

.btn-large {
    padding: 16px 32px;
    font-size: 16px;
}

/* Enhanced Panel Styles */
.panel {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    margin-bottom: 24px;
    overflow: hidden;
}

.panel-heading {
    background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
    border-bottom: 1px solid var(--border-light);
    padding: 20px 24px;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 16px;
}

.panel-body {
    padding: 24px;
}

.panel-footer {
    background: var(--neutral-50);
    border-top: 1px solid var(--border-light);
    padding: 16px 24px;
}

/* Enhanced Navigation Styles */
.navbar {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
    border: none;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.navbar-header {
    background: transparent !important;
}

.navbar-nav > li > a {
    color: var(--text-inverse) !important;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.navbar-nav > li > a:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: var(--text-inverse) !important;
}

/* Enhanced Sidebar Styles */
.sidebar {
    background: var(--bg-primary);
    border-right: 1px solid var(--border-light);
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
}

.sidebar .nav > li > a {
    color: var(--text-secondary);
    padding: 12px 20px;
    border-radius: 8px;
    margin: 4px 12px;
    transition: all 0.2s ease-in-out;
    font-weight: 500;
}

.sidebar .nav > li > a:hover {
    background: var(--neutral-100);
    color: var(--primary-blue);
}

.sidebar .nav > li.active > a {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
    color: var(--text-inverse);
}

/* Enhanced Form Styles */
.form-control {
    border: 2px solid var(--border-light);
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    transition: all 0.2s ease-in-out;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.form-control:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
}

/* Enhanced Alert Styles */
.alert {
    border-radius: 8px;
    padding: 16px 20px;
    margin-bottom: 20px;
    border: none;
    font-weight: 500;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: #065f46;
    border-left: 4px solid var(--accent-emerald);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #92400e;
    border-left: 4px solid var(--accent-amber);
}

.alert-danger {
    background: rgba(244, 63, 94, 0.1);
    color: #991b1b;
    border-left: 4px solid var(--accent-rose);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    color: #1e40af;
    border-left: 4px solid var(--primary-blue);
}

/* Enhanced Modal Styles */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modal-header {
    background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
    border-bottom: 1px solid var(--border-light);
    border-radius: 12px 12px 0 0;
    padding: 20px 24px;
}

.modal-title {
    color: var(--text-primary);
    font-weight: 600;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    background: var(--neutral-50);
    border-top: 1px solid var(--border-light);
    border-radius: 0 0 12px 12px;
    padding: 16px 24px;
}

/* Enhanced Breadcrumb Styles */
.breadcrumb {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 16px 20px;
    margin-bottom: 24px;
    font-size: 14px;
}

.breadcrumb a {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 500;
}

.breadcrumb a:hover {
    color: var(--primary-blue-dark);
    text-decoration: underline;
}

/* Enhanced Card Styles */
.card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    overflow: hidden;
    transition: all 0.2s ease-in-out;
}

.card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
}

.card-header {
    background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
    border-bottom: 1px solid var(--border-light);
    padding: 16px 20px;
    font-weight: 600;
    color: var(--text-primary);
}

.card-body {
    padding: 20px;
}

.card-footer {
    background: var(--neutral-50);
    border-top: 1px solid var(--border-light);
    padding: 12px 20px;
}

/* Utility Classes */
.text-primary { color: var(--primary-blue) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-success { color: var(--accent-emerald) !important; }
.text-warning { color: var(--accent-amber) !important; }
.text-danger { color: var(--accent-rose) !important; }
.text-white { color: var(--text-inverse) !important; }

.bg-primary { background-color: var(--primary-blue) !important; }
.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-success { background-color: var(--accent-emerald) !important; }
.bg-warning { background-color: var(--accent-amber) !important; }
.bg-danger { background-color: var(--accent-rose) !important; }
.bg-light { background-color: var(--neutral-100) !important; }
.bg-white { background-color: var(--bg-primary) !important; }

.border-radius-sm { border-radius: 6px; }
.border-radius-md { border-radius: 8px; }
.border-radius-lg { border-radius: 12px; }
.border-radius-xl { border-radius: 16px; }

.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow-md { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }

/* Enhanced Typography */
.font-weight-light { font-weight: 300; }
.font-weight-normal { font-weight: 400; }
.font-weight-medium { font-weight: 500; }
.font-weight-semibold { font-weight: 600; }
.font-weight-bold { font-weight: 700; }

.text-xs { font-size: 12px; }
.text-sm { font-size: 13px; }
.text-base { font-size: 14px; }
.text-lg { font-size: 16px; }
.text-xl { font-size: 18px; }
.text-2xl { font-size: 20px; }

/* Enhanced Spacing */
.p-0 { padding: 0; }
.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }
.p-5 { padding: 20px; }
.p-6 { padding: 24px; }

.m-0 { margin: 0; }
.m-1 { margin: 4px; }
.m-2 { margin: 8px; }
.m-3 { margin: 12px; }
.m-4 { margin: 16px; }
.m-5 { margin: 20px; }
.m-6 { margin: 24px; }

/* Status Indicators */
.status-active {
    background: linear-gradient(135deg, var(--accent-emerald) 0%, #059669 100%);
    color: var(--text-inverse);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-inactive {
    background: linear-gradient(135deg, var(--neutral-400) 0%, var(--neutral-500) 100%);
    color: var(--text-inverse);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background: linear-gradient(135deg, var(--accent-amber) 0%, #d97706 100%);
    color: var(--text-inverse);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Enhanced Header Styles */
.page-header {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--neutral-50) 100%);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.page-header h1 {
    color: var(--text-primary);
    font-weight: 600;
    margin: 0;
    font-size: 24px;
}

/* Enhanced Loading States */
.loading {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--text-muted);
    font-size: 14px;
}

.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-light);
    border-top: 2px solid var(--primary-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modern Admin Body */
.modern-admin-body {
    background: var(--bg-secondary) !important;
    font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif !important;
}

/* Modern Navbar Styles */
.modern-navbar {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%) !important;
    border: none !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.modern-navbar-header {
    background: transparent !important;
    border-radius: 12px;
    padding: 12px 20px;
}

.navbar-brand-img {
    max-height: 60px;
    width: auto;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modern-nav-item {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 8px;
    margin: 8px;
    transition: all 0.2s ease-in-out;
}

.modern-nav-item:hover {
    background: rgba(255, 255, 255, 0.2) !important;
}

.modern-nav-link {
    background: transparent !important;
    color: var(--text-inverse) !important;
    font-weight: 500;
    padding: 12px 16px !important;
    border-radius: 6px;
    transition: all 0.2s ease-in-out;
}

.modern-nav-link:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: var(--text-inverse) !important;
}

/* Modern Dropdown Menu */
.modern-dropdown-menu {
    background: var(--bg-primary) !important;
    border: 1px solid var(--border-light) !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    padding: 8px !important;
    margin-top: 8px !important;
}

.modern-dropdown-item {
    border-radius: 8px;
    margin: 2px 0;
    transition: all 0.2s ease-in-out;
}

.modern-dropdown-item:hover {
    background: var(--neutral-50) !important;
}

.modern-dropdown-item a {
    color: var(--text-primary) !important;
    padding: 8px 12px !important;
    border-radius: 6px;
    transition: all 0.2s ease-in-out;
}

.modern-dropdown-item a:hover {
    background: var(--neutral-100) !important;
    color: var(--primary-blue) !important;
    text-decoration: none;
}

.modern-profile-img {
    height: 80px !important;
    width: 80px !important;
    object-fit: cover;
    border: 3px solid var(--border-light);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin: 8px 0 !important;
}

.modern-logout-link {
    color: var(--accent-rose) !important;
    font-weight: 500;
}

.modern-logout-link:hover {
    color: #dc2626 !important;
    background: rgba(244, 63, 94, 0.1) !important;
}

.modern-settings-item {
    margin-top: 8px !important;
}

/* Enhanced Breadcrumb Area */
.modern-breadcrumb {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--neutral-50) 100%);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    padding: 20px 24px;
    margin: 15px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    font-size: 16px;
}

.modern-breadcrumb a {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease-in-out;
}

.modern-breadcrumb a:hover {
    color: var(--primary-blue-dark);
    text-decoration: underline;
}

.modern-breadcrumb .current-page {
    color: var(--accent-rose);
    font-weight: 600;
}

/* Modern Content Wrapper */
.modern-content-wrapper {
    width: 98%;
    margin: 1px 15px 15px 15px;
    background: var(--bg-primary);
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid var(--border-light);
}

.modern-wrapper {
    width: 98%;
    margin-left: 15px;
    height: 100%;
}

.modern-sidebar {
    margin-top: 15px;
    border: 1px solid var(--border-light);
    border-radius: 12px;
    background: var(--bg-primary);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* Modern Footer */
.modern-footer {
    width: 98%;
    margin: 0px 15px 15px 15px;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--neutral-50) 100%);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    font-size: 14px;
}

.modern-footer-link {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease-in-out;
}

.modern-footer-link:hover {
    color: var(--primary-blue-dark);
    text-decoration: underline;
}

/* Modern Page Header */
.modern-page-header {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--neutral-50) 100%);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.modern-page-header h1 {
    color: var(--text-primary);
    font-weight: 600;
    margin: 0;
    font-size: 28px;
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Modern Panel Styling */
.modern-panel {
    border: 1px solid var(--border-light);
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    overflow: hidden;
    background: var(--bg-primary);
}

.modern-panel-heading {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
    color: var(--text-inverse);
    padding: 20px 24px;
    font-weight: 600;
    font-size: 18px;
    border: none;
    display: flex;
    align-items: center;
    gap: 12px;
}

.modern-panel .panel-body {
    padding: 24px;
}

.modern-panel .panel-footer {
    background: var(--neutral-50);
    border-top: 1px solid var(--border-light);
    padding: 16px 24px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-admin-body {
        padding: 12px !important;
    }

    .panel-heading, .panel-body, .panel-footer {
        padding: 16px;
    }

    .btn {
        padding: 10px 16px;
        font-size: 13px;
    }

    .page-header {
        padding: 16px;
    }

    .page-header h1 {
        font-size: 20px;
    }

    .bdt > thead > tr > th,
    .bdt > tbody > tr > td {
        padding: 12px 8px;
        font-size: 12px;
    }

    .modern-breadcrumb {
        margin: 8px;
        padding: 16px;
        font-size: 14px;
    }

    .navbar-brand-img {
        max-height: 40px;
    }

    .modern-profile-img {
        height: 60px !important;
        width: 60px !important;
    }
}

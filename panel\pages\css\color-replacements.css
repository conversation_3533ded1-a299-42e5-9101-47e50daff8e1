/* Color Replacement Guide for CBT Admin Panel */
/* Use this as a reference for updating hardcoded colors in PHP files */

/* OLD COLORS TO REPLACE:
   #F8FFBF (bright yellow background) -> var(--bg-secondary) or .modern-admin-body
   #F70505 (harsh red text) -> var(--accent-rose) or .current-page
   #FFEACA (light orange background) -> var(--neutral-100) or .modern-breadcrumb
   #28b2bc (teal header) -> var(--primary-blue) or .modern-panel-heading
   #1ABA09 (green status) -> var(--accent-emerald) or .status-active
   #280BDE (blue text) -> var(--primary-blue) or .text-primary
   #0714F7 (blue text) -> var(--primary-blue) or .text-primary
   #675B80 (purple text) -> var(--text-secondary) or .text-secondary
   #EEE9F5 (light purple bg) -> var(--neutral-100) or .modern-dropdown-menu
   #e4e4e2 (light gray) -> var(--border-light)
   #ffca01 (yellow navbar) -> var(--primary-blue) or .modern-navbar
   #364145 (dark border) -> var(--border-dark)
*/

/* MODERN REPLACEMENT CLASSES */

/* Text Colors */
.modern-text-primary { color: var(--text-primary) !important; }
.modern-text-secondary { color: var(--text-secondary) !important; }
.modern-text-accent { color: var(--accent-rose) !important; }
.modern-text-success { color: var(--accent-emerald) !important; }
.modern-text-warning { color: var(--accent-amber) !important; }
.modern-text-info { color: var(--primary-blue) !important; }

/* Background Colors */
.modern-bg-primary { background-color: var(--bg-primary) !important; }
.modern-bg-secondary { background-color: var(--bg-secondary) !important; }
.modern-bg-accent { background-color: var(--neutral-100) !important; }

/* Status Colors */
.modern-status-active {
    color: var(--accent-emerald) !important;
    font-weight: 600;
}

.modern-status-inactive {
    color: var(--neutral-500) !important;
    font-weight: 600;
}

/* Header Styles */
.modern-header-primary {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%) !important;
    color: var(--text-inverse) !important;
    padding: 16px 20px;
    border-radius: 8px;
    font-weight: 600;
}

.modern-header-secondary {
    background: linear-gradient(135deg, var(--neutral-100) 0%, var(--neutral-200) 100%) !important;
    color: var(--text-primary) !important;
    padding: 16px 20px;
    border-radius: 8px;
    font-weight: 600;
    border: 1px solid var(--border-light);
}

/* Message Styles */
.modern-message-success {
    background: rgba(16, 185, 129, 0.1);
    color: #065f46;
    border-left: 4px solid var(--accent-emerald);
    padding: 12px 16px;
    border-radius: 6px;
    margin: 12px 0;
}

.modern-message-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #92400e;
    border-left: 4px solid var(--accent-amber);
    padding: 12px 16px;
    border-radius: 6px;
    margin: 12px 0;
}

.modern-message-error {
    background: rgba(244, 63, 94, 0.1);
    color: #991b1b;
    border-left: 4px solid var(--accent-rose);
    padding: 12px 16px;
    border-radius: 6px;
    margin: 12px 0;
}

.modern-message-info {
    background: rgba(59, 130, 246, 0.1);
    color: #1e40af;
    border-left: 4px solid var(--primary-blue);
    padding: 12px 16px;
    border-radius: 6px;
    margin: 12px 0;
}

/* Form Enhancements */
.modern-form-group {
    margin-bottom: 20px;
}

.modern-form-label {
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
    font-size: 14px;
}

.modern-form-control {
    border: 2px solid var(--border-light);
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    transition: all 0.2s ease-in-out;
    background: var(--bg-primary);
    color: var(--text-primary);
    width: 100%;
}

.modern-form-control:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

/* Table Enhancements */
.modern-table {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--border-light);
    background: var(--bg-primary);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.modern-table thead {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
}

.modern-table thead th {
    color: var(--text-inverse);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 16px 20px;
    border: none;
    font-size: 13px;
}

.modern-table tbody td {
    color: var(--text-primary);
    padding: 16px 20px;
    border-color: var(--border-light);
    vertical-align: middle;
    font-size: 14px;
}

.modern-table tbody tr:hover {
    background-color: var(--neutral-50);
    transition: background-color 0.2s ease-in-out;
}

/* Card Enhancements */
.modern-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    overflow: hidden;
    transition: all 0.2s ease-in-out;
    margin-bottom: 20px;
}

.modern-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
}

.modern-card-header {
    background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
    border-bottom: 1px solid var(--border-light);
    padding: 16px 20px;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 16px;
}

.modern-card-body {
    padding: 20px;
}

.modern-card-footer {
    background: var(--neutral-50);
    border-top: 1px solid var(--border-light);
    padding: 12px 20px;
}

/* Navigation Enhancements */
.modern-nav-link {
    color: var(--text-secondary);
    padding: 12px 16px;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;
    font-weight: 500;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
}

.modern-nav-link:hover {
    background: var(--neutral-100);
    color: var(--primary-blue);
    text-decoration: none;
}

.modern-nav-link.active {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
    color: var(--text-inverse);
}

/* Badge Styles */
.modern-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modern-badge-primary {
    background: var(--primary-blue);
    color: var(--text-inverse);
}

.modern-badge-success {
    background: var(--accent-emerald);
    color: var(--text-inverse);
}

.modern-badge-warning {
    background: var(--accent-amber);
    color: var(--text-inverse);
}

.modern-badge-danger {
    background: var(--accent-rose);
    color: var(--text-inverse);
}

.modern-badge-secondary {
    background: var(--neutral-400);
    color: var(--text-inverse);
}

/* Utility Classes for Quick Updates */
.remove-old-bg { background: none !important; }
.remove-old-color { color: inherit !important; }
.remove-old-border { border: none !important; }

/* Responsive Utilities */
@media (max-width: 768px) {
    .modern-card-header,
    .modern-card-body,
    .modern-card-footer {
        padding: 16px;
    }
    
    .modern-table thead th,
    .modern-table tbody td {
        padding: 12px 8px;
        font-size: 12px;
    }
    
    .modern-form-control {
        padding: 10px 12px;
    }
}

<?php
// Detailed Excel debugging
echo "<h2>Detailed Excel Debug</h2>";

include "excel_reader2.php";

$template_file = "../../file-excel/bee_kelas_temp.xls";

if (file_exists($template_file)) {
    echo "<h3>Debugging Template File: $template_file</h3>";
    
    try {
        $data = new Spreadsheet_Excel_Reader($template_file);
        
        echo "<h4>Internal Data Structure:</h4>";
        
        // Check if sheets array exists and what it contains
        if (isset($data->sheets)) {
            echo "<p>Sheets array exists with " . count($data->sheets) . " sheets</p>";
            
            foreach ($data->sheets as $sheet_index => $sheet_data) {
                echo "<h5>Sheet $sheet_index:</h5>";
                echo "<ul>";
                
                if (isset($sheet_data['numRows'])) {
                    echo "<li>numRows: " . $sheet_data['numRows'] . "</li>";
                } else {
                    echo "<li>numRows: NOT SET</li>";
                }
                
                if (isset($sheet_data['numCols'])) {
                    echo "<li>numCols: " . $sheet_data['numCols'] . "</li>";
                } else {
                    echo "<li>numCols: NOT SET</li>";
                }
                
                if (isset($sheet_data['maxrow'])) {
                    echo "<li>maxrow: " . $sheet_data['maxrow'] . "</li>";
                } else {
                    echo "<li>maxrow: NOT SET</li>";
                }
                
                if (isset($sheet_data['maxcol'])) {
                    echo "<li>maxcol: " . $sheet_data['maxcol'] . "</li>";
                } else {
                    echo "<li>maxcol: NOT SET</li>";
                }
                
                if (isset($sheet_data['cells'])) {
                    echo "<li>cells: array with " . count($sheet_data['cells']) . " rows</li>";
                    
                    // Show first few cells
                    $cell_count = 0;
                    foreach ($sheet_data['cells'] as $row => $cols) {
                        if ($cell_count > 10) break;
                        if (is_array($cols)) {
                            foreach ($cols as $col => $value) {
                                echo "<li>Cell [$row][$col]: '" . htmlspecialchars($value) . "'</li>";
                                $cell_count++;
                                if ($cell_count > 10) break;
                            }
                        }
                    }
                } else {
                    echo "<li>cells: NOT SET</li>";
                }
                
                echo "</ul>";
            }
        } else {
            echo "<p style='color: red;'>Sheets array does not exist!</p>";
        }
        
        // Test the functions
        echo "<h4>Function Results:</h4>";
        $baris = $data->rowcount(0);
        $kolom = $data->colcount(0);
        echo "<p>rowcount(0): $baris</p>";
        echo "<p>colcount(0): $kolom</p>";
        
        // Try to read some cells manually
        echo "<h4>Manual Cell Reading:</h4>";
        for ($i = 1; $i <= 5; $i++) {
            for ($j = 1; $j <= 5; $j++) {
                $value = $data->val($i, $j, 0);
                if ($value !== "") {
                    echo "<p>Cell [$i][$j]: '" . htmlspecialchars($value) . "'</p>";
                }
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>Template file not found</p>";
}

// Test with uploaded file if available
if (isset($_FILES['userfile']) && $_FILES['userfile']['error'] == UPLOAD_ERR_OK) {
    echo "<h3>Debugging Uploaded File: " . $_FILES['userfile']['name'] . "</h3>";
    
    try {
        $data = new Spreadsheet_Excel_Reader($_FILES['userfile']['tmp_name']);
        
        // Same debugging as above
        if (isset($data->sheets)) {
            echo "<p>Sheets array exists with " . count($data->sheets) . " sheets</p>";
            
            foreach ($data->sheets as $sheet_index => $sheet_data) {
                echo "<h5>Sheet $sheet_index:</h5>";
                echo "<ul>";
                
                foreach (['numRows', 'numCols', 'maxrow', 'maxcol'] as $key) {
                    if (isset($sheet_data[$key])) {
                        echo "<li>$key: " . $sheet_data[$key] . "</li>";
                    } else {
                        echo "<li>$key: NOT SET</li>";
                    }
                }
                
                if (isset($sheet_data['cells'])) {
                    echo "<li>cells: array with " . count($sheet_data['cells']) . " rows</li>";
                    
                    // Show actual cell content
                    $cell_count = 0;
                    foreach ($sheet_data['cells'] as $row => $cols) {
                        if ($cell_count > 20) break;
                        if (is_array($cols)) {
                            foreach ($cols as $col => $value) {
                                echo "<li>Cell [$row][$col]: '" . htmlspecialchars($value) . "'</li>";
                                $cell_count++;
                                if ($cell_count > 20) break;
                            }
                        }
                    }
                } else {
                    echo "<li>cells: NOT SET</li>";
                }
                
                echo "</ul>";
            }
        }
        
        $baris = $data->rowcount(0);
        $kolom = $data->colcount(0);
        echo "<p>rowcount(0): $baris</p>";
        echo "<p>colcount(0): $kolom</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<h3>Upload a file for detailed debugging:</h3>";
    echo '<form method="post" enctype="multipart/form-data">';
    echo '<input name="userfile" type="file" accept=".xls" required>';
    echo '<input type="submit" value="Debug File">';
    echo '</form>';
}
?>

<?php
	if(!isset($_COOKIE['beeuser'])){
	header("Location: login.php");}

if(isset($_REQUEST['modul'])){
	if($_REQUEST['modul']=="upl_kelas"){
	$kata = "Data Kelas"; }
	elseif($_REQUEST['modul']=="upl_mapel"){
	$kata = "Data Mata Pelajaran"; }
	elseif($_REQUEST['modul']=="upl_siswa"){
	$kata = "Data Siswa"; }
}
?>
 <!-- /.row -->
            <div class="row">
                <div class="col-lg-10" style="margin-top:10px;">
                    <div class="panel panel-green">
                        <div class="panel-heading">
                           Download File Excel (Template Data Kelas)
                        </div>
                        <div class="panel-body">
<div style="width: 20%; float:left">
   <a href="../../file-excel/bee_kelas_temp.xls" target="_blank"><img src="images/xls.png" style=" width:90%; max-width:100px;padding-right:10px;"/></a>
</div>

<div style="width: 80%; float:right">
   Silahkan Klik logo Excel disamping, untuk <b>download </b> file excel database Kelas. 
   <br><span style="color: #ff0000;">Jangan ada inputan apapun setelah nomer terakhir</span>  Karena akan dibaca dan diacak oleh sistem. <p>Setelah selesai edit, Upload kembali untuk ditransfer ke
   database melalui tool dibawah ini. 
   
</div>
                        </div>
                        <div class="panel-footer">
       <a href="../../file-excel/bee_kelas_temp.xls" target="_blank"><button class="btn btn-success btn-lg btn-small" id="baru" value="Buat" name="baru"><i class="fa fa-cloud-download"></i>
                            Download Tempalte</button></a>
        
        <a href="?modul=daftar_kelas"><button class="btn btn-success btn-lg btn-small" id="baru" value="Buat" name="baru"><i class="fa fa-list"></i>
                            Lihat Data Kelas</button></a>
                        </div>
                    </div>
                    <!-- /.col-lg-4 -->
                </div>
            </div>
            <!-- /.row -->
            
            
              <div class="row">
                <div class="col-lg-10" style="margin-top:10px;">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                           Upload Template Excel - Data Kelas 
                           
                        </div>
                        <div class="panel-body">
						<form method="post" enctype="multipart/form-data" action="<?php echo "?modul=uploadkelas"; ?>">
                        File Excel Daftar Kelas : 
                        <table border="0" width="78%" cellpadding="20px" cellspacing="20px"><tr><td width="30%"><input name="userfile" type="file" class="btn btn-default" style="width:250px"></td><td>
                        &nbsp;<input name="upload" type="submit" value="Import"  class="btn btn-info" style="margin-top:0px">
                        </td></tr></table>
                        </form>
                        <div style="margin-top:10px;">Persentase Proses Upload <? echo $kata; ?> </div>
<!-- Progress bar holder -->
<div id="progress" style="width:75%; border:1px solid #ccc; padding:5px; margin-top:10px; height:33px"></div>
<!-- Progress information -->
<div id="information" style="width"></div>

<?php

if($_REQUEST['modul']=="uploadkelas"){
// menggunakan class phpExcelReader
include "excel_reader2.php";

// Check if file was uploaded successfully
if (!isset($_FILES['userfile']) || $_FILES['userfile']['error'] != UPLOAD_ERR_OK) {
    echo "<div style='color: red; font-weight: bold;'>Error: File upload failed!</div>";
    if (isset($_FILES['userfile']['error'])) {
        echo "<div>Upload error code: " . $_FILES['userfile']['error'] . "</div>";
    }
    exit;
}

// Check if uploaded file exists and is readable
if (!file_exists($_FILES['userfile']['tmp_name']) || !is_readable($_FILES['userfile']['tmp_name'])) {
    echo "<div style='color: red; font-weight: bold;'>Error: Uploaded file is not readable!</div>";
    exit;
}

// membaca file excel yang diupload
try {
    $data = new Spreadsheet_Excel_Reader($_FILES['userfile']['tmp_name']);
} catch (Exception $e) {
    echo "<div style='color: red; font-weight: bold;'>Error creating Excel reader: " . $e->getMessage() . "</div>";
    exit;
}

// membaca jumlah baris dari data excel
$baris = $data->rowcount(0);

echo "<div>Debug: Excel file has $baris rows detected</div>";

// Check if we have any data in the sheets array
if (isset($data->sheets[0]['cells']) && is_array($data->sheets[0]['cells'])) {
    $actual_rows = count($data->sheets[0]['cells']);
    echo "<div>Debug: Found $actual_rows rows in cells array</div>";

    // Show first few cells for debugging
    $cell_count = 0;
    foreach ($data->sheets[0]['cells'] as $row => $cols) {
        if ($cell_count > 5) break;
        if (is_array($cols)) {
            foreach ($cols as $col => $value) {
                echo "<div>Debug: Cell [$row][$col] = '" . htmlspecialchars($value) . "'</div>";
                $cell_count++;
                if ($cell_count > 5) break;
            }
        }
    }

    // If we have cells but rowcount returns 0, use the actual row count
    if ($baris == 0 && $actual_rows > 0) {
        $max_row = max(array_keys($data->sheets[0]['cells']));
        $baris = $max_row;
        echo "<div>Debug: Adjusted row count to $baris based on actual data</div>";
    }
}

if ($baris < 2) {
    echo "<div style='color: orange; font-weight: bold;'>Warning: Excel file appears to have only $baris rows. Attempting to process anyway...</div>";
    // Don't exit, let's try to process what we have
}

// Include database connection
include "../../config/server.php";

// nilai awal counter untuk jumlah data yang sukses dan yang gagal diimport
$sukses = 0;
$gagal = 0;

$query0 = "TRUNCATE TABLE cbt_kelas";
$hasil0 = mysql_query($query0);

if (!$hasil0) {
    echo "<div style='color: red; font-weight: bold;'>Error truncating table: " . mysql_error() . "</div>";
} else {
    echo "<div>Successfully cleared existing class data</div>";
}

echo "<br><table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Status</th><th>Detail</th></tr>";

// import data excel mulai baris ke-2 (karena baris pertama adalah nama kolom)
// If baris is 0, try to find actual data in cells array
if ($baris == 0 && isset($data->sheets[0]['cells'])) {
    $max_row = 0;
    foreach ($data->sheets[0]['cells'] as $row => $cols) {
        $max_row = max($max_row, $row);
    }
    $baris = $max_row;
    echo "<div>Debug: Found actual max row: $baris</div>";
}

echo "<div>Processing rows 2 to $baris...</div>";

for ($i=2; $i<=$baris; $i++)
{
    echo "<div>Debug: Processing row $i</div>";
  // membaca data soalid (kolom ke-1 FIELD)
//  $fieldz = $data->val($i, 1);
  // membaca data pertanyaan (kolom ke-2 R)
  $x1 = $data->val($i, 1);
  $x2 = $data->val($i, 2);
  $x3 = $data->val($i, 3);
  $x4 = $data->val($i, 4);
  $x5 = $data->val($i, 5);

  echo "<div>Debug Row $i: x1='$x1', x2='$x2', x3='$x3', x4='$x4', x5='$x5'</div>";
// $xlevel = str_replace("'","\'",$xlevel);
 //$xkelas = str_replace("'","\'",$xkelas);
 
 $xlevel = mysql_real_escape_string($x2);
 $xkelas = mysql_real_escape_string($x4);
 
 if(!$x1==""){
		  // setelah data dibaca, sisipkan ke dalam tabel cbt_kelas
		  $query = "INSERT INTO cbt_kelas ( XKodeKelas, XKodeLevel, XNamaKelas, XKodeJurusan, XStatusKelas, XKodeSekolah) VALUES ('$x1','$x2', '$x3', '$x4','1', '$x5')";
		  $hasil = mysql_query($query);

		  if ($hasil) {
		      $sukses++;
		      echo "<tr><td>Berhasil Insert data Kelas <b>$x3</b></td><td><font color=green>Sukses</font></td></tr>";
		  } else {
		      $gagal++;
		      echo "<tr><td>Gagal Insert data Kelas <b>$x3</b></td><td><font color=red>Error: " . mysql_error() . "</font></td></tr>";
		  }
 } else {
     $gagal++;
     echo "<tr><td>Baris $i diabaikan</td><td><font color=orange>Kode kelas kosong</font></td></tr>";
 }
			// Calculate the percentation
			$percent = intval($i/$baris * 100)."%";
    
    // Javascript for updating the progress bar and information
    echo '<script language="javascript">
    document.getElementById("progress").innerHTML="<div style=\"width:'.$percent.';background-image:url(images/pbar-ani1.gif);\">&nbsp;</div>";
    document.getElementById("information").innerHTML="  Proses Entri : '.$xkelas.' ... <b>'.$i.'</b> row(s) of <b>'. $baris.'</b> processed.";
    </script>';
// This is for the buffer achieve the minimum size in order to flush data
    echo str_repeat(' ',1024*64);
    

// Send output to browser immediately
    flush();
// Tell user that the process is completed
   echo '<script language="javascript">document.getElementById("information").innerHTML=" Proses update database Kelas : Completed"</script>';
  
  }
  // jika proses insert data sukses, maka counter $sukses bertambah
  // jika gagal, maka counter $gagal yang bertambah

echo "</table>";

// tampilan status sukses dan gagal
?>
<div style="width:75%; margin-top:10px">
    <div class="alert alert-success">
    <?php
    echo "<p>Jumlah data yang sukses diimport : ".$sukses."<br>";
    ?>
    </div>
    
    <?php
        if($gagal>0){
        ?>
        <div class="alert alert-danger">
        <?php
        echo "Jumlah data yang gagal diimport : ".$gagal."</p>";
        ?></div>
        <?php
        }
    }
    ?>
	</div>
 
</div>

                    </div>
                    <!-- /.col-lg-4 -->
                </div>

            </div>
            <!-- /.row -->
            

            

<script src="../../mesin/js/jquery.js"></script>

<?php
if(!isset($_COOKIE['beeuser'])){
    header("Location: login.php");
}

if(isset($_REQUEST['modul'])){
    if($_REQUEST['modul']=="upl_kelas"){
        $kata = "Data Kelas"; 
    } elseif($_REQUEST['modul']=="upl_mapel"){
        $kata = "Data Mata Pelajaran"; 
    } elseif($_REQUEST['modul']=="upl_siswa"){
        $kata = "Data Siswa"; 
    }
}
?>

<div class="row">
    <div class="col-lg-10" style="margin-top:10px;">
        <div class="panel panel-green">
            <div class="panel-heading">
               Download Template Excel/CSV Kelas
            </div>
            <div class="panel-body">
                <div style="width: 100%;">
                   <h4>Download Template:</h4>
                   <div style="margin: 10px 0;">
                       <a href="../../file-excel/template_kelas_new.xls" target="_blank" class="btn btn-success" style="margin-right: 10px;">
                           📊 Download Template Excel (.xls)
                       </a>
                       <a href="../../file-excel/template_kelas.csv" target="_blank" class="btn btn-info">
                           📄 Download Template CSV
                       </a>
                   </div>
                   <div style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #007bff; margin: 10px 0;">
                       <strong>Format Data:</strong> XKodeKelas | XKodeLevel | XNamaKelas | XKodeJurusan | XKodeSekolah
                       <br><strong>Rekomendasi:</strong>
                       <ul style="margin: 5px 0;">
                           <li>Untuk Excel: Gunakan template Excel yang sudah dioptimasi</li>
                           <li>Untuk CSV: Lebih reliable tapi kurang user-friendly untuk editing</li>
                       </ul>
                   </div>
                </div>
                <div style="clear:both;"></div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-10" style="margin-top:10px;">
        <div class="panel panel-default">
            <div class="panel-heading">
                Upload Excel/CSV - Kelas (Mendukung .xls dan .csv)
            </div>
            <div class="panel-body">
                <form method="post" enctype="multipart/form-data" action="<?php echo "?modul=uploadkelas"; ?>">
                File Excel/CSV Daftar Kelas : 
                <table border="0" width="78%" cellpadding="20px" cellspacing="20px">
                    <tr>
                        <td width="30%">
                            <input name="userfile" type="file" class="btn btn-default" style="width:250px" accept=".csv,.xls">
                        </td>
                        <td>
                            &nbsp;<input name="upload" type="submit" value="Import"  class="btn btn-info" style="margin-top:0px">
                        </td>
                    </tr>
                </table>
                </form>
                <div style="margin-top:10px;">Persentase Proses Upload <?php echo isset($kata) ? $kata : 'Data Kelas'; ?></div>
                
                <!-- Progress bar holder -->
                <div id="progress" style="width:75%; border:1px solid #ccc; padding:5px; margin-top:10px; height:33px"></div>
                <!-- Progress information -->
                <div id="information" style="width"></div>

<?php
if($_REQUEST['modul']=="uploadkelas"){
    
    // Check if file was uploaded successfully
    if (!isset($_FILES['userfile']) || $_FILES['userfile']['error'] != UPLOAD_ERR_OK) {
        echo "<div style='color: red; font-weight: bold;'>Error: File upload failed!</div>";
        if (isset($_FILES['userfile']['error'])) {
            echo "<div>Upload error code: " . $_FILES['userfile']['error'] . "</div>";
        }
        exit;
    }

    $file_path = $_FILES['userfile']['tmp_name'];
    $file_name = $_FILES['userfile']['name'];
    $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

    echo "<div><strong>Processing file:</strong> $file_name (.$file_ext)</div>";
    echo "<div><strong>File size:</strong> " . $_FILES['userfile']['size'] . " bytes</div>";
    echo "<div><strong>File type:</strong> " . $_FILES['userfile']['type'] . "</div>";

    // Debug: Check file content
    if ($file_ext == 'xls') {
        $file_content = file_get_contents($file_path);
        $header = substr($file_content, 0, 8);
        $hex_header = bin2hex($header);
        echo "<div><strong>File header (hex):</strong> $hex_header</div>";

        // Check if it's a valid OLE file
        if (substr($file_content, 0, 8) == "\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1") {
            echo "<div style='color: green;'>✓ Valid Excel OLE file format detected</div>";
        } else {
            echo "<div style='color: orange;'>⚠ File header doesn't match standard Excel format</div>";
        }
    }

    // Include database connection
    include "../../config/server.php";
    
    $data_rows = array();
    $sukses = 0;
    $gagal = 0;

    // Process different file formats
    if ($file_ext == 'csv') {
        // Process CSV file
        echo "<div>Reading CSV file...</div>";
        
        $handle = fopen($file_path, 'r');
        if ($handle) {
            $row_num = 0;
            while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $row_num++;
                
                // Skip header row
                if ($row_num == 1) {
                    continue;
                }
                
                // Ensure we have 5 columns
                if (count($data) >= 5) {
                    $data_rows[] = array(
                        'row' => $row_num,
                        'x1' => trim($data[0]), // XKodeKelas
                        'x2' => trim($data[1]), // XKodeLevel
                        'x3' => trim($data[2]), // XNamaKelas
                        'x4' => trim($data[3]), // XKodeJurusan
                        'x5' => trim($data[4])  // XKodeSekolah
                    );
                }
            }
            fclose($handle);
        } else {
            echo "<div style='color: red;'>Error: Cannot read CSV file</div>";
            exit;
        }
        
    } else if ($file_ext == 'xls') {
        // Process Excel file with improved method
        echo "<div>Reading Excel file...</div>";

        include "excel_reader2.php";

        try {
            $excel_data = new Spreadsheet_Excel_Reader($file_path);

            // Debug: Show internal structure
            echo "<div style='color: blue;'>Debug: Checking Excel file structure...</div>";

            // Debug: Check if sheets exist
            if (isset($excel_data->sheets)) {
                echo "<div style='color: blue;'>Debug: Found " . count($excel_data->sheets) . " sheets</div>";

                if (isset($excel_data->sheets[0])) {
                    echo "<div style='color: blue;'>Debug: Sheet 0 exists</div>";

                    // Show all properties of sheet 0
                    foreach ($excel_data->sheets[0] as $key => $value) {
                        if ($key == 'cells' && is_array($value)) {
                            echo "<div style='color: blue;'>Debug: $key = array with " . count($value) . " rows</div>";
                        } else {
                            echo "<div style='color: blue;'>Debug: $key = " . (is_array($value) ? '[array]' : $value) . "</div>";
                        }
                    }
                } else {
                    echo "<div style='color: red;'>Debug: Sheet 0 does not exist!</div>";
                }
            } else {
                echo "<div style='color: red;'>Debug: No sheets found!</div>";
            }

            // Multiple methods to get row count
            $baris = 0;
            $method_used = "";

            // Method 1: Try rowcount function
            $baris = $excel_data->rowcount(0);
            if ($baris > 0) {
                $method_used = "rowcount()";
            }

            // Method 2: Check sheets array directly
            if ($baris == 0 && isset($excel_data->sheets[0])) {
                if (isset($excel_data->sheets[0]['numRows']) && $excel_data->sheets[0]['numRows'] > 0) {
                    $baris = $excel_data->sheets[0]['numRows'];
                    $method_used = "numRows";
                } elseif (isset($excel_data->sheets[0]['maxrow']) && $excel_data->sheets[0]['maxrow'] > 0) {
                    $baris = $excel_data->sheets[0]['maxrow'];
                    $method_used = "maxrow";
                }
            }

            // Method 3: Count actual cells
            if ($baris == 0 && isset($excel_data->sheets[0]['cells'])) {
                $max_row = 0;
                $cell_count = 0;
                foreach ($excel_data->sheets[0]['cells'] as $row => $cols) {
                    if (is_array($cols) && count($cols) > 0) {
                        $max_row = max($max_row, $row);
                        $cell_count += count($cols);
                    }
                }
                if ($max_row > 0) {
                    $baris = $max_row;
                    $method_used = "cell counting ($cell_count cells found)";
                }
            }

            echo "<div style='color: blue;'>Debug: Found $baris rows using method: $method_used</div>";

            // Method 4: Try to read cells directly even if rowcount is 0
            if ($baris == 0) {
                echo "<div style='color: orange;'>Warning: No rows detected, trying direct cell access...</div>";

                // Show first few cells for debugging
                echo "<div style='color: blue;'>Debug: Testing direct cell access...</div>";
                echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                echo "<tr><th>Row</th><th>Col 1</th><th>Col 2</th><th>Col 3</th><th>Col 4</th><th>Col 5</th></tr>";

                for ($test_row = 1; $test_row <= 10; $test_row++) {
                    echo "<tr>";
                    echo "<td>$test_row</td>";

                    $has_data = false;
                    for ($test_col = 1; $test_col <= 5; $test_col++) {
                        $test_val = $excel_data->val($test_row, $test_col);
                        echo "<td>" . htmlspecialchars($test_val) . "</td>";
                        if (trim($test_val) != "") {
                            $has_data = true;
                            $baris = max($baris, $test_row);
                        }
                    }
                    echo "</tr>";

                    if (!$has_data && $test_row > 3) {
                        break; // Stop if no data found after row 3
                    }
                }
                echo "</table>";

                if ($baris > 0) {
                    $method_used = "direct cell access";
                    echo "<div style='color: blue;'>Debug: Found $baris rows using direct access</div>";
                }
            }

            if ($baris > 1) {
                echo "<div style='color: green;'>Successfully detected $baris rows in Excel file</div>";

                // Read data starting from row 2 (skip header)
                for ($i = 2; $i <= $baris; $i++) {
                    $x1 = trim($excel_data->val($i, 1));
                    $x2 = trim($excel_data->val($i, 2));
                    $x3 = trim($excel_data->val($i, 3));
                    $x4 = trim($excel_data->val($i, 4));
                    $x5 = trim($excel_data->val($i, 5));

                    // Only add row if it has some data
                    if ($x1 != "" || $x2 != "" || $x3 != "" || $x4 != "" || $x5 != "") {
                        $data_rows[] = array(
                            'row' => $i,
                            'x1' => $x1,
                            'x2' => $x2,
                            'x3' => $x3,
                            'x4' => $x4,
                            'x5' => $x5
                        );
                    }
                }

                echo "<div style='color: green;'>Successfully extracted " . count($data_rows) . " data rows</div>";

            } else {
                echo "<div style='color: red;'>Error: Excel file tidak memiliki data yang bisa dibaca</div>";
                echo "<div style='color: orange;'><strong>Tips untuk memperbaiki:</strong></div>";
                echo "<ul>";
                echo "<li>Pastikan file Excel dalam format .xls (Excel 97-2003)</li>";
                echo "<li>Pastikan data dimulai dari baris 2 (baris 1 untuk header)</li>";
                echo "<li>Pastikan ada data di kolom A (XKodeKelas)</li>";
                echo "<li>Coba save ulang file sebagai Excel 97-2003 Workbook (.xls)</li>";
                echo "<li>Atau convert ke CSV untuk compatibility yang lebih baik</li>";
                echo "</ul>";
                exit;
            }

        } catch (Exception $e) {
            echo "<div style='color: red;'>Excel parsing error: " . $e->getMessage() . "</div>";

            // Try alternative method: read as tab-separated values
            echo "<div style='color: orange;'>Trying alternative method: reading as tab-separated file...</div>";

            $handle = fopen($file_path, 'r');
            if ($handle) {
                $row_num = 0;
                $alternative_success = false;

                while (($line = fgets($handle)) !== false && $row_num < 20) {
                    $row_num++;

                    // Try tab-separated
                    $data = explode("\t", trim($line));

                    // Skip header row
                    if ($row_num == 1) {
                        if (count($data) >= 5) {
                            echo "<div style='color: blue;'>Found tab-separated header: " . implode(' | ', array_slice($data, 0, 5)) . "</div>";
                            $alternative_success = true;
                        }
                        continue;
                    }

                    // Process data rows
                    if (count($data) >= 5 && $alternative_success) {
                        $data_rows[] = array(
                            'row' => $row_num,
                            'x1' => trim($data[0]),
                            'x2' => trim($data[1]),
                            'x3' => trim($data[2]),
                            'x4' => trim($data[3]),
                            'x5' => trim($data[4])
                        );
                    }
                }
                fclose($handle);

                if ($alternative_success && count($data_rows) > 0) {
                    echo "<div style='color: green;'>✓ Alternative method successful! Found " . count($data_rows) . " rows</div>";
                } else {
                    echo "<div style='color: red;'>Alternative method also failed</div>";
                    echo "<div style='color: orange;'><strong>Solusi:</strong></div>";
                    echo "<ul>";
                    echo "<li>File mungkin corrupt atau format tidak didukung</li>";
                    echo "<li>Coba buka file di Excel dan save ulang sebagai Excel 97-2003 (.xls)</li>";
                    echo "<li>Atau convert ke CSV format untuk compatibility terbaik</li>";
                    echo "<li>Pastikan data dipisahkan dengan tab atau koma</li>";
                    echo "</ul>";
                    exit;
                }
            } else {
                echo "<div style='color: red;'>Cannot read file with alternative method</div>";
                exit;
            }
        }
        
    } else {
        echo "<div style='color: red;'>Error: Format file tidak didukung</div>";
        echo "<div style='color: orange;'><strong>Format yang didukung:</strong></div>";
        echo "<ul>";
        echo "<li><strong>.csv</strong> - Paling reliable dan mudah dibuat</li>";
        echo "<li><strong>.xls</strong> - Excel 97-2003 format (BUKAN .xlsx)</li>";
        echo "</ul>";

        if ($file_ext == 'xlsx') {
            echo "<div style='background-color: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
            echo "<strong>⚠ File .xlsx tidak didukung!</strong><br>";
            echo "<strong>Cara convert .xlsx ke .xls:</strong>";
            echo "<ol>";
            echo "<li>Buka file .xlsx di Microsoft Excel</li>";
            echo "<li>Klik File → Save As</li>";
            echo "<li>Pilih format: <strong>Excel 97-2003 Workbook (*.xls)</strong></li>";
            echo "<li>Save dan upload file .xls yang baru</li>";
            echo "</ol>";
            echo "<strong>Atau:</strong> Save sebagai CSV untuk compatibility terbaik";
            echo "</div>";
        }
        exit;
    }

    $total_rows = count($data_rows);
    echo "<div><strong>Successfully read $total_rows data rows</strong></div>";

    if ($total_rows == 0) {
        echo "<div style='color: red;'>Error: Tidak ada data yang bisa diproses</div>";
        exit;
    }

    // Clear existing data
    $query0 = "TRUNCATE TABLE cbt_kelas";
    $hasil0 = mysql_query($query0);

    if (!$hasil0) {
        echo "<div style='color: red; font-weight: bold;'>Error truncating table: " . mysql_error() . "</div>";
        exit;
    } else {
        echo "<div style='color: green;'>Successfully cleared existing class data</div>";
    }

    echo "<br><table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Status</th><th>Detail</th></tr>";

    // Process the data rows
    foreach ($data_rows as $index => $row_data) {
        $i = $row_data['row'];
        $x1 = $row_data['x1']; // XKodeKelas
        $x2 = $row_data['x2']; // XKodeLevel
        $x3 = $row_data['x3']; // XNamaKelas
        $x4 = $row_data['x4']; // XKodeJurusan
        $x5 = $row_data['x5']; // XKodeSekolah
        
        if ($x1 != "") {
            // Escape data for SQL injection prevention
            $x1_safe = mysql_real_escape_string($x1);
            $x2_safe = mysql_real_escape_string($x2);
            $x3_safe = mysql_real_escape_string($x3);
            $x4_safe = mysql_real_escape_string($x4);
            $x5_safe = mysql_real_escape_string($x5);
            
            // Insert data into database
            $query = "INSERT INTO cbt_kelas (XKodeKelas, XKodeLevel, XNamaKelas, XKodeJurusan, XStatusKelas, XKodeSekolah) VALUES ('$x1_safe','$x2_safe', '$x3_safe', '$x4_safe','1', '$x5_safe')";
            $hasil = mysql_query($query);
            
            if ($hasil) {
                $sukses++;
                echo "<tr><td style='color: green;'>✓ Berhasil</td><td>Insert data Kelas <b>$x3</b> ($x1)</td></tr>";
            } else {
                $gagal++;
                echo "<tr><td style='color: red;'>✗ Gagal</td><td>Insert data Kelas <b>$x3</b> - Error: " . mysql_error() . "</td></tr>";
            }
            
        } else {
            $gagal++;
            echo "<tr><td style='color: orange;'>⚠ Skip</td><td>Baris $i - Kode kelas kosong</td></tr>";
        }
        
        // Progress update
        $current = $index + 1;
        $percent = intval($current / $total_rows * 100) . "%";
        echo '<script language="javascript">
        document.getElementById("progress").innerHTML="<div style=\"width:'.$percent.';background-image:url(images/pbar-ani1.gif);\">&nbsp;</div>";
        document.getElementById("information").innerHTML="Proses Entri : '.$x3.' ... <b>'.$current.'</b> of <b>'.$total_rows.'</b> processed.";
        </script>';
        echo str_repeat(' ',1024*64);
        flush();
    }

    echo "</table>";
    echo '<script language="javascript">document.getElementById("information").innerHTML="Proses update database Kelas : Completed"</script>';
}
?>

            </div>
        </div>
    </div>
</div>

<!-- Summary -->
<?php if(isset($_REQUEST['modul']) && $_REQUEST['modul']=="uploadkelas"): ?>
<div style="width:75%; margin-top:10px">
    <div class="alert alert-success">
        <h4>✓ Upload Completed!</h4>
        <p><strong>Jumlah data yang sukses diimport:</strong> <?php echo $sukses; ?> kelas<br>
        <?php if($gagal > 0): ?>
        <strong>Jumlah data yang gagal diimport:</strong> <?php echo $gagal; ?> kelas<br>
        <?php endif; ?>
        <strong>Total data diproses:</strong> <?php echo ($sukses + $gagal); ?> kelas</p>
    </div>
    
    <?php if($sukses > 0): ?>
    <div class="alert alert-info">
        <h4>✓ Kelas berhasil ditambahkan!</h4>
        <p>Sekarang Anda bisa melanjutkan untuk upload data siswa.</p>
        <a href="?modul=uploadsiswa" class="btn btn-primary">Upload Data Siswa</a>
    </div>
    <?php endif; ?>
</div>
<?php endif; ?>

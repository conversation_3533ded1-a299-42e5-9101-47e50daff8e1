<?php
if(!isset($_COOKIE['beeuser'])){
    header("Location: login.php");
}

if(isset($_REQUEST['modul'])){
    if($_REQUEST['modul']=="upl_kelas"){
        $kata = "Data Kelas"; 
    } elseif($_REQUEST['modul']=="upl_mapel"){
        $kata = "Data Mata Pelajaran"; 
    } elseif($_REQUEST['modul']=="upl_siswa"){
        $kata = "Data Siswa"; 
    }
}
?>

<div class="row">
    <div class="col-lg-10" style="margin-top:10px;">
        <div class="panel panel-green">
            <div class="panel-heading">
               Download Template Excel/CSV Kelas
            </div>
            <div class="panel-body">
                <div style="width: 20%; float:left">
                   <a href="../../file-excel/template_kelas.csv" target="_blank"><img src="images/csv.png" style=" width:90%; max-width:100px;padding-right:10px;" onerror="this.src='images/xls.png'"/></a>
                </div>
                <div style="width: 80%; float:right">
                   Silahkan Klik logo CSV disamping, untuk <b> download </b> template CSV database Kelas.
                   <br><span style="color: #ff0000;">Format: XKodeKelas | XKodeLevel | XNamaKelas | XKodeJurusan | XKodeSekolah</span>
                   <p>Setelah selesai edit, Upload kembali untuk ditransfer ke database melalui tool dibawah ini.
                   <br><strong>Tips:</strong> CSV format lebih reliable daripada Excel. Bisa diedit dengan Excel atau Notepad.
                </div>
                <div style="clear:both;"></div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-10" style="margin-top:10px;">
        <div class="panel panel-default">
            <div class="panel-heading">
                Upload Excel/CSV - Kelas (Mendukung .xls dan .csv)
            </div>
            <div class="panel-body">
                <form method="post" enctype="multipart/form-data" action="<?php echo "?modul=uploadkelas"; ?>">
                File Excel/CSV Daftar Kelas : 
                <table border="0" width="78%" cellpadding="20px" cellspacing="20px">
                    <tr>
                        <td width="30%">
                            <input name="userfile" type="file" class="btn btn-default" style="width:250px" accept=".csv,.xls">
                        </td>
                        <td>
                            &nbsp;<input name="upload" type="submit" value="Import"  class="btn btn-info" style="margin-top:0px">
                        </td>
                    </tr>
                </table>
                </form>
                <div style="margin-top:10px;">Persentase Proses Upload <?php echo $kata; ?></div>
                
                <!-- Progress bar holder -->
                <div id="progress" style="width:75%; border:1px solid #ccc; padding:5px; margin-top:10px; height:33px"></div>
                <!-- Progress information -->
                <div id="information" style="width"></div>

<?php
if($_REQUEST['modul']=="uploadkelas"){
    
    // Check if file was uploaded successfully
    if (!isset($_FILES['userfile']) || $_FILES['userfile']['error'] != UPLOAD_ERR_OK) {
        echo "<div style='color: red; font-weight: bold;'>Error: File upload failed!</div>";
        if (isset($_FILES['userfile']['error'])) {
            echo "<div>Upload error code: " . $_FILES['userfile']['error'] . "</div>";
        }
        exit;
    }

    $file_path = $_FILES['userfile']['tmp_name'];
    $file_name = $_FILES['userfile']['name'];
    $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

    echo "<div><strong>Processing file:</strong> $file_name (.$file_ext)</div>";

    // Include database connection
    include "../../config/server.php";
    
    $data_rows = array();
    $sukses = 0;
    $gagal = 0;

    // Process different file formats
    if ($file_ext == 'csv') {
        // Process CSV file
        echo "<div>Reading CSV file...</div>";
        
        $handle = fopen($file_path, 'r');
        if ($handle) {
            $row_num = 0;
            while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $row_num++;
                
                // Skip header row
                if ($row_num == 1) {
                    continue;
                }
                
                // Ensure we have 5 columns
                if (count($data) >= 5) {
                    $data_rows[] = array(
                        'row' => $row_num,
                        'x1' => trim($data[0]), // XKodeKelas
                        'x2' => trim($data[1]), // XKodeLevel
                        'x3' => trim($data[2]), // XNamaKelas
                        'x4' => trim($data[3]), // XKodeJurusan
                        'x5' => trim($data[4])  // XKodeSekolah
                    );
                }
            }
            fclose($handle);
        } else {
            echo "<div style='color: red;'>Error: Cannot read CSV file</div>";
            exit;
        }
        
    } else if ($file_ext == 'xls') {
        // Process Excel file
        echo "<div>Reading Excel file...</div>";
        
        include "excel_reader2.php";
        
        try {
            $excel_data = new Spreadsheet_Excel_Reader($file_path);
            
            // Try to get data using multiple methods
            $baris = $excel_data->rowcount(0);
            
            // Fallback method if rowcount returns 0
            if ($baris == 0 && isset($excel_data->sheets[0]['cells'])) {
                $max_row = 0;
                foreach ($excel_data->sheets[0]['cells'] as $row => $cols) {
                    if (is_array($cols) && count($cols) > 0) {
                        $max_row = max($max_row, $row);
                    }
                }
                $baris = $max_row;
            }
            
            echo "<div>Found $baris rows in Excel file</div>";
            
            if ($baris > 1) {
                for ($i = 2; $i <= $baris; $i++) {
                    $x1 = trim($excel_data->val($i, 1));
                    $x2 = trim($excel_data->val($i, 2));
                    $x3 = trim($excel_data->val($i, 3));
                    $x4 = trim($excel_data->val($i, 4));
                    $x5 = trim($excel_data->val($i, 5));
                    
                    $data_rows[] = array(
                        'row' => $i,
                        'x1' => $x1,
                        'x2' => $x2,
                        'x3' => $x3,
                        'x4' => $x4,
                        'x5' => $x5
                    );
                }
            } else {
                echo "<div style='color: red;'>Error: Excel file tidak memiliki data atau tidak bisa dibaca</div>";
                echo "<div style='color: orange;'><strong>Solusi:</strong> Coba convert file Excel ke CSV format dan upload ulang</div>";
                exit;
            }
            
        } catch (Exception $e) {
            echo "<div style='color: red;'>Excel parsing error: " . $e->getMessage() . "</div>";
            echo "<div style='color: orange;'><strong>Solusi:</strong> Coba convert file Excel ke CSV format dan upload ulang</div>";
            exit;
        }
        
    } else {
        echo "<div style='color: red;'>Error: Format file tidak didukung. Gunakan .csv atau .xls saja</div>";
        exit;
    }

    $total_rows = count($data_rows);
    echo "<div><strong>Successfully read $total_rows data rows</strong></div>";

    if ($total_rows == 0) {
        echo "<div style='color: red;'>Error: Tidak ada data yang bisa diproses</div>";
        exit;
    }

    // Clear existing data
    $query0 = "TRUNCATE TABLE cbt_kelas";
    $hasil0 = mysql_query($query0);

    if (!$hasil0) {
        echo "<div style='color: red; font-weight: bold;'>Error truncating table: " . mysql_error() . "</div>";
        exit;
    } else {
        echo "<div style='color: green;'>Successfully cleared existing class data</div>";
    }

    echo "<br><table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Status</th><th>Detail</th></tr>";

    // Process the data rows
    foreach ($data_rows as $index => $row_data) {
        $i = $row_data['row'];
        $x1 = $row_data['x1']; // XKodeKelas
        $x2 = $row_data['x2']; // XKodeLevel
        $x3 = $row_data['x3']; // XNamaKelas
        $x4 = $row_data['x4']; // XKodeJurusan
        $x5 = $row_data['x5']; // XKodeSekolah
        
        if ($x1 != "") {
            // Escape data for SQL injection prevention
            $x1_safe = mysql_real_escape_string($x1);
            $x2_safe = mysql_real_escape_string($x2);
            $x3_safe = mysql_real_escape_string($x3);
            $x4_safe = mysql_real_escape_string($x4);
            $x5_safe = mysql_real_escape_string($x5);
            
            // Insert data into database
            $query = "INSERT INTO cbt_kelas (XKodeKelas, XKodeLevel, XNamaKelas, XKodeJurusan, XStatusKelas, XKodeSekolah) VALUES ('$x1_safe','$x2_safe', '$x3_safe', '$x4_safe','1', '$x5_safe')";
            $hasil = mysql_query($query);
            
            if ($hasil) {
                $sukses++;
                echo "<tr><td style='color: green;'>✓ Berhasil</td><td>Insert data Kelas <b>$x3</b> ($x1)</td></tr>";
            } else {
                $gagal++;
                echo "<tr><td style='color: red;'>✗ Gagal</td><td>Insert data Kelas <b>$x3</b> - Error: " . mysql_error() . "</td></tr>";
            }
            
        } else {
            $gagal++;
            echo "<tr><td style='color: orange;'>⚠ Skip</td><td>Baris $i - Kode kelas kosong</td></tr>";
        }
        
        // Progress update
        $current = $index + 1;
        $percent = intval($current / $total_rows * 100) . "%";
        echo '<script language="javascript">
        document.getElementById("progress").innerHTML="<div style=\"width:'.$percent.';background-image:url(images/pbar-ani1.gif);\">&nbsp;</div>";
        document.getElementById("information").innerHTML="Proses Entri : '.$x3.' ... <b>'.$current.'</b> of <b>'.$total_rows.'</b> processed.";
        </script>';
        echo str_repeat(' ',1024*64);
        flush();
    }

    echo "</table>";
    echo '<script language="javascript">document.getElementById("information").innerHTML="Proses update database Kelas : Completed"</script>';
}
?>

            </div>
        </div>
    </div>
</div>

<!-- Summary -->
<?php if(isset($_REQUEST['modul']) && $_REQUEST['modul']=="uploadkelas"): ?>
<div style="width:75%; margin-top:10px">
    <div class="alert alert-success">
        <h4>✓ Upload Completed!</h4>
        <p><strong>Jumlah data yang sukses diimport:</strong> <?php echo $sukses; ?> kelas<br>
        <?php if($gagal > 0): ?>
        <strong>Jumlah data yang gagal diimport:</strong> <?php echo $gagal; ?> kelas<br>
        <?php endif; ?>
        <strong>Total data diproses:</strong> <?php echo ($sukses + $gagal); ?> kelas</p>
    </div>
    
    <?php if($sukses > 0): ?>
    <div class="alert alert-info">
        <h4>✓ Kelas berhasil ditambahkan!</h4>
        <p>Sekarang Anda bisa melanjutkan untuk upload data siswa.</p>
        <a href="?modul=uploadsiswa" class="btn btn-primary">Upload Data Siswa</a>
    </div>
    <?php endif; ?>
</div>
<?php endif; ?>

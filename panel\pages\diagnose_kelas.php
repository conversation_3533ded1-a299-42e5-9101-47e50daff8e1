<?php
// Diagnostic tool for class upload
echo "<h2>Class Upload Diagnostic Tool</h2>";

// Step 1: Check database connection
echo "<h3>Step 1: Database Connection Test</h3>";
try {
    include "../../config/server.php";
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Test basic query
    $test_query = mysql_query("SELECT COUNT(*) as count FROM cbt_kelas");
    if ($test_query) {
        $result = mysql_fetch_array($test_query);
        echo "<p>✓ Current classes in database: " . $result['count'] . "</p>";
        
        // Show existing classes if any
        if ($result['count'] > 0) {
            echo "<h4>Existing Classes:</h4>";
            $classes_query = mysql_query("SELECT * FROM cbt_kelas LIMIT 10");
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th><PERSON><PERSON></th><th><PERSON><PERSON></th><th><PERSON><PERSON></th><th><PERSON><PERSON></th><th><PERSON>de <PERSON></th></tr>";
            while ($class = mysql_fetch_array($classes_query)) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($class['XKodeKelas']) . "</td>";
                echo "<td>" . htmlspecialchars($class['XKodeLevel']) . "</td>";
                echo "<td>" . htmlspecialchars($class['XNamaKelas']) . "</td>";
                echo "<td>" . htmlspecialchars($class['XKodeJurusan']) . "</td>";
                echo "<td>" . htmlspecialchars($class['XKodeSekolah']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p style='color: red;'>✗ Error querying cbt_kelas table: " . mysql_error() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
}

// Step 2: Check Excel reader
echo "<h3>Step 2: Excel Reader Test</h3>";
try {
    include "excel_reader2.php";
    echo "<p style='color: green;'>✓ Excel reader loaded successfully</p>";
    
    // Test with template file
    $template_file = "../../file-excel/bee_kelas_temp.xls";
    if (file_exists($template_file)) {
        echo "<p>✓ Template file exists: $template_file</p>";
        
        $data = new Spreadsheet_Excel_Reader($template_file);
        $baris = $data->rowcount(0);
        $kolom = $data->colcount(0);
        
        echo "<p>✓ Template readable - Rows: $baris, Columns: $kolom</p>";
        
        // Show template structure
        echo "<h4>Template Structure:</h4>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Row</th><th>Col 1</th><th>Col 2</th><th>Col 3</th><th>Col 4</th><th>Col 5</th></tr>";
        for ($i = 1; $i <= min(5, $baris); $i++) {
            echo "<tr>";
            echo "<td><strong>$i</strong></td>";
            for ($j = 1; $j <= min(5, $kolom); $j++) {
                $value = $data->val($i, $j, 0);
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>Expected columns:</strong></p>";
        echo "<ul>";
        echo "<li>Column 1: XKodeKelas (Class Code)</li>";
        echo "<li>Column 2: XKodeLevel (Level Code)</li>";
        echo "<li>Column 3: XNamaKelas (Class Name)</li>";
        echo "<li>Column 4: XKodeJurusan (Major Code)</li>";
        echo "<li>Column 5: XKodeSekolah (School Code)</li>";
        echo "</ul>";
        
    } else {
        echo "<p style='color: red;'>✗ Template file not found: $template_file</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Excel reader error: " . $e->getMessage() . "</p>";
}

// Step 3: File upload test
echo "<h3>Step 3: File Upload Test</h3>";

if (isset($_FILES['userfile']) && $_FILES['userfile']['error'] == UPLOAD_ERR_OK) {
    echo "<p>✓ File uploaded: " . $_FILES['userfile']['name'] . "</p>";
    echo "<p>✓ File size: " . $_FILES['userfile']['size'] . " bytes</p>";
    
    try {
        $data = new Spreadsheet_Excel_Reader($_FILES['userfile']['tmp_name']);
        $baris = $data->rowcount(0);
        $kolom = $data->colcount(0);
        
        echo "<p>✓ File readable - Rows: $baris, Columns: $kolom</p>";
        
        if ($baris > 0) {
            echo "<h4>File Content Analysis:</h4>";
            
            // Check data starting from row 2 (as per upload script)
            $valid_rows = 0;
            $empty_rows = 0;
            
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Row</th><th>Kode Kelas</th><th>Kode Level</th><th>Nama Kelas</th><th>Kode Jurusan</th><th>Kode Sekolah</th><th>Status</th></tr>";
            
            for ($i = 2; $i <= min(10, $baris); $i++) {
                $x1 = $data->val($i, 1); // XKodeKelas
                $x2 = $data->val($i, 2); // XKodeLevel
                $x3 = $data->val($i, 3); // XNamaKelas
                $x4 = $data->val($i, 4); // XKodeJurusan
                $x5 = $data->val($i, 5); // XKodeSekolah
                
                echo "<tr>";
                echo "<td>$i</td>";
                echo "<td>" . htmlspecialchars($x1) . "</td>";
                echo "<td>" . htmlspecialchars($x2) . "</td>";
                echo "<td>" . htmlspecialchars($x3) . "</td>";
                echo "<td>" . htmlspecialchars($x4) . "</td>";
                echo "<td>" . htmlspecialchars($x5) . "</td>";
                
                if ($x1 != "") {
                    echo "<td style='color: green;'>Valid</td>";
                    $valid_rows++;
                } else {
                    echo "<td style='color: red;'>Empty Code</td>";
                    $empty_rows++;
                }
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<p><strong>Summary:</strong></p>";
            echo "<ul>";
            echo "<li>Valid rows found: $valid_rows</li>";
            echo "<li>Empty rows found: $empty_rows</li>";
            echo "<li>Total data rows (2 to $baris): " . ($baris - 1) . "</li>";
            echo "</ul>";
            
            if ($valid_rows == 0) {
                echo "<p style='color: red;'>⚠ <strong>ISSUE FOUND:</strong> No valid class codes found in column 1!</p>";
                echo "<p>Make sure your Excel file has class codes in the first column starting from row 2.</p>";
            }
            
        } else {
            echo "<p style='color: red;'>✗ File has no data rows</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error reading uploaded file: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p>No file uploaded. Upload an Excel file to test:</p>";
    echo '<form method="post" enctype="multipart/form-data">';
    echo '<input name="userfile" type="file" accept=".xls" required>';
    echo '<input type="submit" value="Diagnose Class File" style="margin-left: 10px;">';
    echo '</form>';
    
    if (isset($_FILES['userfile'])) {
        echo "<p style='color: red;'>Upload error code: " . $_FILES['userfile']['error'] . "</p>";
    }
}

// Step 4: Recommendations
echo "<h3>Step 4: Recommendations</h3>";
echo "<div style='background-color: #f0f8ff; padding: 15px; border-left: 4px solid #0066cc;'>";
echo "<h4>To fix class upload issues:</h4>";
echo "<ol>";
echo "<li><strong>Use the correct template:</strong> Download bee_kelas_temp.xls</li>";
echo "<li><strong>Check file format:</strong> Must be .xls (not .xlsx)</li>";
echo "<li><strong>Ensure data starts from row 2:</strong> Row 1 is header</li>";
echo "<li><strong>Class code required:</strong> Column 1 must have class codes</li>";
echo "<li><strong>Fill all columns:</strong> All 5 columns should have data</li>";
echo "</ol>";
echo "</div>";
?>

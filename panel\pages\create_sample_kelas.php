<?php
// Create a sample class Excel file for testing
echo "<h2>Create Sample Class Data</h2>";

// Sample class data
$sample_classes = [
    ['XKodeKelas', 'XKodeLevel', 'XNamaKelas', 'XKodeJurus<PERSON>', 'XKodeSekolah'],
    ['10A', '10', 'Kelas 10 A', 'UMUM', 'WU25512'],
    ['10B', '10', 'Kelas 10 B', 'UMUM', 'WU25512'],
    ['11IPA1', '11', 'Kelas 11 IPA 1', 'IPA', 'WU25512'],
    ['11IPA2', '11', 'Kelas 11 IPA 2', 'IPA', 'WU25512'],
    ['11IPS1', '11', 'Kelas 11 IPS 1', 'IPS', 'WU25512'],
    ['12IPA1', '12', 'Kelas 12 IPA 1', 'IPA', 'WU25512'],
    ['12IPA2', '12', 'Kelas 12 IPA 2', 'IPA', 'WU25512'],
    ['12IPS1', '12', 'Kelas 12 IPS 1', 'IPS', 'WU25512'],
];

if (isset($_POST['create_csv'])) {
    // Create CSV file that can be opened in Excel and saved as .xls
    $filename = "sample_kelas_" . date('Y-m-d_H-i-s') . ".csv";
    $filepath = "../../file-excel/" . $filename;
    
    $file = fopen($filepath, 'w');
    
    if ($file) {
        foreach ($sample_classes as $row) {
            fputcsv($file, $row);
        }
        fclose($file);
        
        echo "<div style='color: green; font-weight: bold;'>✓ Sample CSV file created: $filename</div>";
        echo "<p>You can:</p>";
        echo "<ol>";
        echo "<li>Download the file: <a href='../../file-excel/$filename' target='_blank'>$filename</a></li>";
        echo "<li>Open it in Excel</li>";
        echo "<li>Save it as .xls format (Excel 97-2003 Workbook)</li>";
        echo "<li>Upload the .xls file to test the class upload</li>";
        echo "</ol>";
    } else {
        echo "<div style='color: red;'>Error creating file</div>";
    }
}

if (isset($_POST['insert_direct'])) {
    // Insert sample data directly into database
    include "../../config/server.php";
    
    echo "<h3>Inserting Sample Data Directly</h3>";
    
    // Clear existing data
    $query0 = "TRUNCATE TABLE cbt_kelas";
    $hasil0 = mysql_query($query0);
    
    if ($hasil0) {
        echo "<p>✓ Cleared existing class data</p>";
        
        $sukses = 0;
        $gagal = 0;
        
        // Skip header row
        for ($i = 1; $i < count($sample_classes); $i++) {
            $row = $sample_classes[$i];
            $query = "INSERT INTO cbt_kelas (XKodeKelas, XKodeLevel, XNamaKelas, XKodeJurusan, XStatusKelas, XKodeSekolah) VALUES ('{$row[0]}', '{$row[1]}', '{$row[2]}', '{$row[3]}', '1', '{$row[4]}')";
            
            $hasil = mysql_query($query);
            if ($hasil) {
                $sukses++;
                echo "<p>✓ Inserted: {$row[2]} ({$row[0]})</p>";
            } else {
                $gagal++;
                echo "<p style='color: red;'>✗ Failed: {$row[2]} - " . mysql_error() . "</p>";
            }
        }
        
        echo "<div style='background-color: #d4edda; padding: 10px; margin: 10px 0; border: 1px solid #c3e6cb;'>";
        echo "<strong>Summary:</strong><br>";
        echo "Successfully inserted: $sukses classes<br>";
        echo "Failed: $gagal classes";
        echo "</div>";
        
        if ($sukses > 0) {
            echo "<p style='color: green; font-weight: bold;'>✓ Sample classes have been added to the database!</p>";
            echo "<p>You can now try uploading student data.</p>";
        }
        
    } else {
        echo "<p style='color: red;'>Error clearing table: " . mysql_error() . "</p>";
    }
}

echo "<h3>Sample Class Data Preview:</h3>";
echo "<table border='1' style='border-collapse: collapse;'>";
foreach ($sample_classes as $index => $row) {
    echo "<tr>";
    if ($index == 0) {
        foreach ($row as $cell) {
            echo "<th>" . htmlspecialchars($cell) . "</th>";
        }
    } else {
        foreach ($row as $cell) {
            echo "<td>" . htmlspecialchars($cell) . "</td>";
        }
    }
    echo "</tr>";
}
echo "</table>";

echo "<h3>Options:</h3>";
echo '<form method="post">';
echo '<button type="submit" name="create_csv" style="margin: 5px; padding: 10px; background-color: #007bff; color: white; border: none; cursor: pointer;">Create CSV File (for Excel conversion)</button><br>';
echo '<button type="submit" name="insert_direct" style="margin: 5px; padding: 10px; background-color: #28a745; color: white; border: none; cursor: pointer;">Insert Sample Data Directly to Database</button>';
echo '</form>';

echo "<div style='background-color: #f8f9fa; padding: 15px; margin: 15px 0; border-left: 4px solid #007bff;'>";
echo "<h4>Recommendation:</h4>";
echo "<p>Since the Excel reader seems to have issues with the template file, I recommend:</p>";
echo "<ol>";
echo "<li><strong>Use 'Insert Sample Data Directly'</strong> to add some test classes to the database</li>";
echo "<li>This will allow you to test student uploads immediately</li>";
echo "<li>Once you confirm the system works, you can create proper Excel files for bulk uploads</li>";
echo "</ol>";
echo "</div>";
?>

a {
  color: var(--primary-blue);
  border-bottom: 1px solid var(--primary-blue-light);
  text-decoration: none;
  transition: all 0.2s ease-in-out; }
  a:hover {
    color: var(--primary-blue-dark);
    border-bottom: 1px solid var(--border-medium); }

h1, h2, p, pre {
  margin-bottom: 15px; }

h1 {
  text-align: center;
  color: var(--primary-blue);
  font-weight: 600; }

pre {
  background: var(--neutral-100);
  padding: 20px;
  display: inline-block;
  font-family: 'Inter', 'Source Code Pro', monospace;
  font-size: 14px;
  border-radius: 8px;
  overflow-x: auto;
  width: 100%;
  box-sizing: border-box;
  border: 1px solid var(--border-light);
  color: var(--text-primary); }

hr {
  border-bottom: 1px solid var(--border-light);
  margin: 30px 0; }

#image-preview {
  width: 400px;
  height: 400px;
  position: relative;
  overflow: hidden;
  background-color: var(--bg-primary);
  color: var(--text-secondary);
  border: 2px dashed var(--border-medium);
  border-radius: 12px;
  transition: all 0.2s ease-in-out; }
  #image-preview:hover {
    border-color: var(--primary-blue);
    background-color: var(--neutral-50); }
  #image-preview input {
    line-height: 200px;
    font-size: 200px;
    position: absolute;
    opacity: 0;
    z-index: 10;
    cursor: pointer; }
  #image-preview label {
    position: absolute;
    z-index: 5;
    opacity: 0.9;
    cursor: pointer;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
    color: var(--text-inverse);
    width: 200px;
    height: 50px;
    font-size: 16px;
    font-weight: 500;
    line-height: 50px;
    text-transform: uppercase;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    text-align: center;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); }

#audio-preview {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: 8px;
  width: auto;
  padding: 20px;
  display: inline-block;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }

#audio-upload {
  cursor: pointer;
  background: linear-gradient(135deg, var(--accent-emerald) 0%, #059669 100%);
  color: var(--text-inverse);
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 500;
  text-transform: uppercase;
  border-radius: 8px;
  border: none;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); }

#audio-upload:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15); }

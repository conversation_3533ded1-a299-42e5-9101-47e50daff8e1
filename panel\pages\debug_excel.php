<?php
// Debug script to test Excel reading
include "excel_reader2.php";

// Test with a sample file path - you'll need to replace this with an actual Excel file path
$test_file = "../../file-excel/test.xls"; // Replace with actual file path

echo "<h3>Excel Reader Debug</h3>";

if (isset($_FILES['userfile']['tmp_name']) && $_FILES['userfile']['tmp_name']) {
    $file_path = $_FILES['userfile']['tmp_name'];
    echo "<p>Testing with uploaded file: " . $_FILES['userfile']['name'] . "</p>";
} else {
    echo "<p>No file uploaded. Please upload an Excel file to test.</p>";
    echo '<form method="post" enctype="multipart/form-data">';
    echo '<input name="userfile" type="file" accept=".xls,.xlsx">';
    echo '<input type="submit" value="Test Excel File">';
    echo '</form>';
    exit;
}

try {
    echo "<p>Creating Spreadsheet_Excel_Reader object...</p>";
    $data = new Spreadsheet_Excel_Reader($file_path);
    
    echo "<p>Excel file loaded successfully!</p>";
    
    // Check if sheets array exists
    echo "<p>Checking sheets array...</p>";
    if (isset($data->sheets)) {
        echo "<p>Sheets array exists. Number of sheets: " . count($data->sheets) . "</p>";
        
        // Check sheet 0 specifically
        if (isset($data->sheets[0])) {
            echo "<p>Sheet 0 exists.</p>";
            
            // Check what properties exist for sheet 0
            echo "<p>Sheet 0 properties:</p><ul>";
            foreach ($data->sheets[0] as $key => $value) {
                if (is_array($value)) {
                    echo "<li>$key: [array with " . count($value) . " elements]</li>";
                } else {
                    echo "<li>$key: $value</li>";
                }
            }
            echo "</ul>";
        } else {
            echo "<p>Sheet 0 does not exist!</p>";
        }
    } else {
        echo "<p>Sheets array does not exist!</p>";
    }
    
    // Test rowcount and colcount
    echo "<p>Testing rowcount()...</p>";
    $baris = $data->rowcount(0);
    echo "<p>Row count: $baris</p>";
    
    echo "<p>Testing colcount()...</p>";
    $kolom = $data->colcount(0);
    echo "<p>Column count: $kolom</p>";
    
    // Test reading some cell values
    if ($baris > 0) {
        echo "<p>Testing cell values (first few rows):</p>";
        echo "<table border='1'>";
        for ($i = 1; $i <= min(5, $baris); $i++) {
            echo "<tr>";
            for ($j = 1; $j <= min(5, $kolom); $j++) {
                $value = $data->val($i, $j, 0);
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>

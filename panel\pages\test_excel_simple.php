<?php
// Simple test to verify Excel reading works
include "excel_reader2.php";

echo "<h3>Simple Excel Test</h3>";

// Test with the template file first
$template_file = "../../file-excel/bee_siswa_temp.xls";

if (file_exists($template_file)) {
    echo "<h4>Testing Template File: $template_file</h4>";
    
    try {
        $data = new Spreadsheet_Excel_Reader($template_file);
        $baris = $data->rowcount(0);
        $kolom = $data->colcount(0);
        
        echo "<p>✓ Template loaded successfully</p>";
        echo "<p>✓ Rows: $baris, Columns: $kolom</p>";
        
        // Show first few rows
        echo "<table border='1'>";
        for ($i = 1; $i <= min(5, $baris); $i++) {
            echo "<tr>";
            for ($j = 1; $j <= min(10, $kolom); $j++) {
                $value = $data->val($i, $j, 0);
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Template file not found: $template_file</p>";
}

// Test file upload if available
if (isset($_FILES['userfile']) && $_FILES['userfile']['error'] == UPLOAD_ERR_OK) {
    echo "<h4>Testing Uploaded File: " . $_FILES['userfile']['name'] . "</h4>";
    
    try {
        $data = new Spreadsheet_Excel_Reader($_FILES['userfile']['tmp_name']);
        $baris = $data->rowcount(0);
        $kolom = $data->colcount(0);
        
        echo "<p>✓ File loaded successfully</p>";
        echo "<p>✓ Rows: $baris, Columns: $kolom</p>";
        
        if ($baris > 0) {
            echo "<p>✓ File has data</p>";
            
            // Test the loop that would be used in upload
            echo "<p>Testing upload loop (rows 3 to $baris):</p>";
            $valid_rows = 0;
            
            for ($i = 3; $i <= $baris; $i++) {
                $xnomer = $data->val($i, 1);
                $xnama = $data->val($i, 2);
                
                $xnomer_clean = str_replace(" ", "", $xnomer);
                
                if ($xnomer_clean != "") {
                    $valid_rows++;
                    echo "<div>Row $i: Valid - Nomer='$xnomer', Nama='$xnama'</div>";
                } else {
                    echo "<div style='color: orange;'>Row $i: Empty - Nomer='$xnomer', Nama='$xnama'</div>";
                }
                
                if ($i > 10) {
                    echo "<div>... (showing first 8 rows only)</div>";
                    break;
                }
            }
            
            echo "<p><strong>Summary: $valid_rows valid rows found</strong></p>";
        } else {
            echo "<p style='color: red;'>✗ File has no data rows</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<h4>Upload a file to test:</h4>";
    echo '<form method="post" enctype="multipart/form-data">';
    echo '<input name="userfile" type="file" accept=".xls">';
    echo '<input type="submit" value="Test File">';
    echo '</form>';
}
?>
